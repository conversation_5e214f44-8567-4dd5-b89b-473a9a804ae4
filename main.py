import yaml
import argparse
import sys
import logging
import json
import csv
import time
import uuid
import concurrent.futures
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from bs4 import BeautifulSoup


class WebpageTester:
    """Comprehensive webpage testing framework"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.driver = None
        self.session_id = str(uuid.uuid4())
        self.logger = None
        self.test_results = {}

    def setup_enhanced_logging(self, log_dir: str, log_level: int) -> Dict[str, Any]:
        """Setup comprehensive logging system"""
        log_path = Path(log_dir)
        log_path.mkdir(exist_ok=True)

        # Create session-specific log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_path / f"webpage_test_{timestamp}_{self.session_id[:8]}.log"

        # Configure logger
        self.logger = logging.getLogger(f"WebpageTester_{self.session_id}")
        self.logger.setLevel(log_level)

        # Clear existing handlers
        self.logger.handlers.clear()

        # File handler
        if self.config.get('logging', {}).get('file_output', True):
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)

        # Console handler
        if self.config.get('logging', {}).get('console_output', True):
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            console_formatter = logging.Formatter(
                '%(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

        self.logger.info(f"Logging session started: {self.session_id}")

        return {
            'session_id': self.session_id,
            'log_directory': str(log_path),
            'log_file': str(log_file)
        }

    def _setup_webdriver(self) -> webdriver:
        """Initialize and configure WebDriver"""
        browser_type = self.config.get('browser', {}).get('type', 'chrome').lower()
        headless = self.config.get('browser', {}).get('headless', False)
        window_size = self.config.get('browser', {}).get('window_size', [1920, 1080])

        try:
            if browser_type == 'chrome':
                options = ChromeOptions()
                if headless:
                    options.add_argument('--headless')
                options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-gpu')

                # Custom user agent if specified
                user_agent = self.config.get('advanced', {}).get('user_agent')
                if user_agent:
                    options.add_argument(f'--user-agent={user_agent}')

                service = ChromeService(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)

            elif browser_type == 'firefox':
                options = FirefoxOptions()
                if headless:
                    options.add_argument('--headless')

                service = FirefoxService(GeckoDriverManager().install())
                driver = webdriver.Firefox(service=service, options=options)
                driver.set_window_size(window_size[0], window_size[1])

            else:
                raise ValueError(f"Unsupported browser type: {browser_type}")

            # Set timeouts
            timeout = self.config.get('browser', {}).get('timeout', 30)
            implicit_wait = self.config.get('browser', {}).get('implicit_wait', 10)

            driver.set_page_load_timeout(timeout)
            driver.implicitly_wait(implicit_wait)

            self.logger.info(f"WebDriver initialized: {browser_type}")
            return driver

        except Exception as e:
            self.logger.error(f"Failed to initialize WebDriver: {e}")
            raise

    def _find_links_in_header(self, url: str) -> List[Dict[str, str]]:
        """Find all links in the header section of a webpage"""
        links = []

        try:
            self.driver.get(url)
            self.logger.info(f"Loaded page: {url}")

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Find header element
            header_selector = self.config.get('testing', {}).get('header_selector', 'header')
            try:
                header_element = self.driver.find_element(By.CSS_SELECTOR, header_selector)
            except:
                # Fallback to common header selectors
                for selector in ['header', '.header', '#header', 'nav', '.nav']:
                    try:
                        header_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        break
                    except:
                        continue
                else:
                    self.logger.warning(f"No header element found on {url}")
                    return links

            # Find all links in header
            link_selectors = self.config.get('testing', {}).get('link_selectors', ['a[href]'])
            for selector in link_selectors:
                try:
                    link_elements = header_element.find_elements(By.CSS_SELECTOR, selector)
                    for link_element in link_elements:
                        href = link_element.get_attribute('href')
                        text = link_element.text.strip()
                        if href:
                            links.append({
                                'url': href,
                                'text': text,
                                'selector': selector
                            })
                except Exception as e:
                    self.logger.warning(f"Error finding links with selector '{selector}': {e}")

            self.logger.info(f"Found {len(links)} links in header of {url}")

        except TimeoutException:
            self.logger.error(f"Timeout loading page: {url}")
        except Exception as e:
            self.logger.error(f"Error finding links in header of {url}: {e}")

        return links

    def _check_link_status(self, link: Dict[str, str]) -> Dict[str, Any]:
        """Check the HTTP status of a link"""
        url = link['url']
        result = {
            'url': url,
            'text': link['text'],
            'selector': link['selector'],
            'status_code': None,
            'status': 'unknown',
            'error': None,
            'response_time': None
        }

        try:
            start_time = time.time()

            # Configure request settings
            timeout = self.config.get('testing', {}).get('request_timeout', 10)
            headers = {'User-Agent': 'Mozilla/5.0 (compatible; WebpageTester/1.0)'}
            custom_headers = self.config.get('advanced', {}).get('custom_headers', {})
            headers.update(custom_headers)

            response = requests.head(url, timeout=timeout, headers=headers, allow_redirects=True)
            response_time = time.time() - start_time

            result.update({
                'status_code': response.status_code,
                'response_time': response_time,
                'status': 'working' if response.status_code < 400 else 'broken'
            })

            if response.status_code >= 400:
                result['error'] = f"HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            result.update({
                'status': 'timeout',
                'error': 'Request timeout'
            })
        except requests.exceptions.ConnectionError:
            result.update({
                'status': 'connection_error',
                'error': 'Connection failed'
            })
        except Exception as e:
            result.update({
                'status': 'error',
                'error': str(e)
            })

        return result

    def _check_broken_links(self, url: str) -> Dict[str, Any]:
        """Check for broken links in the header of a webpage"""
        self.logger.info(f"Checking broken links for: {url}")

        result = {
            'url': url,
            'success': False,
            'links_found': 0,
            'links_working': 0,
            'links_broken': 0,
            'links_timeout': 0,
            'links_error': 0,
            'links': [],
            'error': None
        }

        try:
            # Find links in header
            links = self._find_links_in_header(url)
            result['links_found'] = len(links)

            if not links:
                result['success'] = True
                return result

            # Check each link with concurrent requests
            max_workers = self.config.get('testing', {}).get('max_concurrent_requests', 5)

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_link = {executor.submit(self._check_link_status, link): link for link in links}

                for future in concurrent.futures.as_completed(future_to_link):
                    link_result = future.result()
                    result['links'].append(link_result)

                    # Update counters
                    if link_result['status'] == 'working':
                        result['links_working'] += 1
                    elif link_result['status'] == 'broken':
                        result['links_broken'] += 1
                    elif link_result['status'] == 'timeout':
                        result['links_timeout'] += 1
                    else:
                        result['links_error'] += 1

            result['success'] = True
            self.logger.info(f"Broken link check completed for {url}: "
                           f"{result['links_working']} working, {result['links_broken']} broken")

        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"Error checking broken links for {url}: {e}")

        return result

    def _manage_gtinfo_cookie(self, url: str) -> Dict[str, Any]:
        """Manage gtinfo cookie - find and delete it"""
        self.logger.info(f"Managing gtinfo cookie for: {url}")

        result = {
            'url': url,
            'success': False,
            'cookie_found': False,
            'cookie_deleted': False,
            'cookie_value': None,
            'error': None
        }

        try:
            # Load the page
            self.driver.get(url)

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Check for gtinfo cookie
            cookie_name = self.config.get('testing', {}).get('cookie_name', 'gtinfo')

            try:
                cookie = self.driver.get_cookie(cookie_name)
                if cookie:
                    result['cookie_found'] = True
                    result['cookie_value'] = cookie.get('value')
                    self.logger.info(f"Found {cookie_name} cookie: {cookie['value']}")

                    # Delete the cookie
                    self.driver.delete_cookie(cookie_name)

                    # Verify deletion
                    deleted_cookie = self.driver.get_cookie(cookie_name)
                    if not deleted_cookie:
                        result['cookie_deleted'] = True
                        self.logger.info(f"Successfully deleted {cookie_name} cookie")
                    else:
                        self.logger.warning(f"Failed to delete {cookie_name} cookie")
                else:
                    self.logger.info(f"No {cookie_name} cookie found")

            except Exception as e:
                self.logger.warning(f"Error managing cookie: {e}")
                result['error'] = str(e)

            result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"Error managing gtinfo cookie for {url}: {e}")

        return result

    def run_comprehensive_tests(self, urls: List[str]) -> Dict[str, Any]:
        """Run comprehensive tests on a list of URLs"""
        self.logger.info(f"Starting comprehensive tests for {len(urls)} URLs")

        overall_result = {
            'success': False,
            'results': {
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id,
                'total_urls': len(urls),
                'results': {}
            }
        }

        try:
            # Initialize WebDriver
            self.driver = self._setup_webdriver()

            for url in urls:
                self.logger.info(f"Testing URL: {url}")

                url_result = {
                    'url': url,
                    'success': True,
                    'timestamp': datetime.now().isoformat(),
                    'tests': {}
                }

                # Run broken links test if enabled
                if self.config.get('test_cases', {}).get('broken_links', {}).get('enabled', True):
                    try:
                        broken_links_result = self._check_broken_links(url)
                        url_result['tests']['broken_links'] = broken_links_result
                        if not broken_links_result['success']:
                            url_result['success'] = False
                    except Exception as e:
                        self.logger.error(f"Broken links test failed for {url}: {e}")
                        url_result['tests']['broken_links'] = {
                            'success': False,
                            'error': str(e)
                        }
                        url_result['success'] = False

                # Run gtinfo cookie test if enabled
                if self.config.get('test_cases', {}).get('gtinfo_cookie', {}).get('enabled', True):
                    try:
                        cookie_result = self._manage_gtinfo_cookie(url)
                        url_result['tests']['gtinfo_cookie'] = cookie_result
                        if not cookie_result['success']:
                            url_result['success'] = False
                    except Exception as e:
                        self.logger.error(f"Cookie test failed for {url}: {e}")
                        url_result['tests']['gtinfo_cookie'] = {
                            'success': False,
                            'error': str(e)
                        }
                        url_result['success'] = False

                overall_result['results']['results'][url] = url_result
                self.logger.info(f"Completed testing for {url}: {'PASS' if url_result['success'] else 'FAIL'}")

            overall_result['success'] = True
            self.logger.info("Comprehensive tests completed successfully")

        except Exception as e:
            overall_result['error'] = str(e)
            self.logger.error(f"Comprehensive tests failed: {e}")

        return overall_result

    def generate_all_reports(self, results: Dict[str, Any], output_dir: str, title: str) -> Dict[str, Any]:
        """Generate reports in multiple formats"""
        self.logger.info(f"Generating reports in: {output_dir}")

        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_short = self.session_id[:8]

        report_result = {
            'success': False,
            'output_directory': str(output_path),
            'reports': {}
        }

        try:
            # Generate HTML report
            if self.config.get('reporting', {}).get('generate_html', True):
                html_file = output_path / f"report_{timestamp}_{session_short}.html"
                self._generate_html_report(results, html_file, title)
                report_result['reports']['html'] = str(html_file)

            # Generate JSON report
            if self.config.get('reporting', {}).get('generate_json', True):
                json_file = output_path / f"report_{timestamp}_{session_short}.json"
                self._generate_json_report(results, json_file)
                report_result['reports']['json'] = str(json_file)

            # Generate CSV report
            if self.config.get('reporting', {}).get('generate_csv', True):
                csv_file = output_path / f"report_{timestamp}_{session_short}.csv"
                self._generate_csv_report(results, csv_file)
                report_result['reports']['csv'] = str(csv_file)

            report_result['success'] = True
            self.logger.info("All reports generated successfully")

        except Exception as e:
            report_result['error'] = str(e)
            self.logger.error(f"Report generation failed: {e}")

        return report_result

    def _generate_html_report(self, results: Dict[str, Any], output_file: Path, title: str):
        """Generate HTML report with summary view for many URLs"""

        # Calculate summary statistics
        total_urls = results['total_urls']
        passed_urls = sum(1 for r in results['results'].values() if r['success'])
        failed_urls = total_urls - passed_urls

        total_links = sum(r['tests'].get('broken_links', {}).get('links_found', 0) for r in results['results'].values())
        total_working = sum(r['tests'].get('broken_links', {}).get('links_working', 0) for r in results['results'].values())
        total_broken = sum(r['tests'].get('broken_links', {}).get('links_broken', 0) for r in results['results'].values())
        total_timeout = sum(r['tests'].get('broken_links', {}).get('links_timeout', 0) for r in results['results'].values())
        total_error = sum(r['tests'].get('broken_links', {}).get('links_error', 0) for r in results['results'].values())

        cookies_found = sum(1 for r in results['results'].values() if r['tests'].get('gtinfo_cookie', {}).get('cookie_found', False))
        cookies_deleted = sum(1 for r in results['results'].values() if r['tests'].get('gtinfo_cookie', {}).get('cookie_deleted', False))

        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; }}
        .header h1 {{ margin: 0 0 10px 0; font-size: 2.5em; }}
        .header p {{ margin: 5px 0; opacity: 0.9; }}

        .summary-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .summary-card {{ background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }}
        .summary-card h3 {{ margin: 0 0 10px 0; color: #333; }}
        .summary-card .number {{ font-size: 2.5em; font-weight: bold; margin: 10px 0; }}
        .summary-card .label {{ color: #666; font-size: 0.9em; }}

        .success-number {{ color: #4CAF50; }}
        .failure-number {{ color: #f44336; }}
        .warning-number {{ color: #ff9800; }}
        .info-number {{ color: #2196F3; }}

        .results-section {{ margin-top: 30px; }}
        .results-header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }}
        .filter-buttons {{ display: flex; gap: 10px; }}
        .filter-btn {{ padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; transition: all 0.3s; }}
        .filter-btn:hover {{ background: #f0f0f0; }}
        .filter-btn.active {{ background: #2196F3; color: white; border-color: #2196F3; }}

        .results-table {{ width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .results-table th {{ background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; }}
        .results-table td {{ padding: 12px 15px; border-bottom: 1px solid #e9ecef; }}
        .results-table tr:hover {{ background: #f8f9fa; }}

        .status-badge {{ padding: 4px 12px; border-radius: 20px; font-size: 0.85em; font-weight: 500; }}
        .status-pass {{ background: #d4edda; color: #155724; }}
        .status-fail {{ background: #f8d7da; color: #721c24; }}

        .url-cell {{ max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }}
        .url-cell:hover {{ overflow: visible; white-space: normal; word-break: break-all; }}

        .details-btn {{ background: #17a2b8; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8em; }}
        .details-btn:hover {{ background: #138496; }}

        .details-row {{ display: none; }}
        .details-content {{ background: #f8f9fa; padding: 20px; }}
        .details-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
        .detail-section {{ background: white; padding: 15px; border-radius: 4px; border: 1px solid #e0e0e0; }}
        .detail-section h4 {{ margin: 0 0 10px 0; color: #333; }}

        .links-mini-table {{ width: 100%; font-size: 0.85em; }}
        .links-mini-table th, .links-mini-table td {{ padding: 4px 8px; }}
        .status-working {{ color: #4CAF50; font-weight: 500; }}
        .status-broken {{ color: #f44336; font-weight: 500; }}
        .status-timeout {{ color: #ff9800; font-weight: 500; }}
        .status-error {{ color: #9c27b0; font-weight: 500; }}

        @media (max-width: 768px) {{
            .summary-grid {{ grid-template-columns: 1fr; }}
            .details-grid {{ grid-template-columns: 1fr; }}
            .filter-buttons {{ flex-wrap: wrap; }}
        }}
    </style>
    <script>
        function toggleDetails(rowId) {{
            const detailsRow = document.getElementById('details-' + rowId);
            const btn = document.getElementById('btn-' + rowId);
            if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {{
                detailsRow.style.display = 'table-row';
                btn.textContent = 'Hide Details';
            }} else {{
                detailsRow.style.display = 'none';
                btn.textContent = 'Show Details';
            }}
        }}

        function filterResults(status) {{
            const rows = document.querySelectorAll('.result-row');
            const buttons = document.querySelectorAll('.filter-btn');

            buttons.forEach(btn => btn.classList.remove('active'));
            document.getElementById('filter-' + status).classList.add('active');

            rows.forEach(row => {{
                if (status === 'all' || row.dataset.status === status) {{
                    row.style.display = 'table-row';
                }} else {{
                    row.style.display = 'none';
                    // Hide details row too
                    const detailsRow = document.getElementById('details-' + row.dataset.id);
                    if (detailsRow) detailsRow.style.display = 'none';
                }}
            }});
        }}

        document.addEventListener('DOMContentLoaded', function() {{
            document.getElementById('filter-all').classList.add('active');
        }});
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{title}</h1>
            <p>Generated: {results['timestamp']}</p>
            <p>Session ID: {results['session_id']}</p>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h3>URLs Tested</h3>
                <div class="number info-number">{total_urls}</div>
                <div class="label">Total websites</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="number success-number">{passed_urls}</div>
                <div class="label">Successful tests</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number failure-number">{failed_urls}</div>
                <div class="label">Failed tests</div>
            </div>
            <div class="summary-card">
                <h3>Links Found</h3>
                <div class="number info-number">{total_links}</div>
                <div class="label">Total links checked</div>
            </div>
            <div class="summary-card">
                <h3>Working Links</h3>
                <div class="number success-number">{total_working}</div>
                <div class="label">Healthy links</div>
            </div>
            <div class="summary-card">
                <h3>Broken Links</h3>
                <div class="number failure-number">{total_broken}</div>
                <div class="label">Failed links</div>
            </div>
            <div class="summary-card">
                <h3>Cookies Found</h3>
                <div class="number warning-number">{cookies_found}</div>
                <div class="label">GTInfo cookies</div>
            </div>
            <div class="summary-card">
                <h3>Cookies Deleted</h3>
                <div class="number success-number">{cookies_deleted}</div>
                <div class="label">Successfully removed</div>
            </div>
        </div>

        <div class="results-section">
            <div class="results-header">
                <h2>Detailed Results</h2>
                <div class="filter-buttons">
                    <button id="filter-all" class="filter-btn" onclick="filterResults('all')">All ({total_urls})</button>
                    <button id="filter-pass" class="filter-btn" onclick="filterResults('pass')">Passed ({passed_urls})</button>
                    <button id="filter-fail" class="filter-btn" onclick="filterResults('fail')">Failed ({failed_urls})</button>
                </div>
            </div>

            <table class="results-table">
                <thead>
                    <tr>
                        <th>URL</th>
                        <th>Status</th>
                        <th>Links</th>
                        <th>Broken</th>
                        <th>Cookie</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
"""

        # Generate table rows for each URL
        row_id = 0
        for url, url_result in results['results'].items():
            row_id += 1
            status = 'pass' if url_result['success'] else 'fail'
            status_badge = 'status-pass' if url_result['success'] else 'status-fail'
            status_text = 'PASS' if url_result['success'] else 'FAIL'

            bl_result = url_result['tests'].get('broken_links', {})
            cookie_result = url_result['tests'].get('gtinfo_cookie', {})

            links_found = bl_result.get('links_found', 0)
            links_broken = bl_result.get('links_broken', 0)
            cookie_found = 'Yes' if cookie_result.get('cookie_found', False) else 'No'

            html_content += f"""
                    <tr class="result-row" data-status="{status}" data-id="{row_id}">
                        <td class="url-cell" title="{url}">{url}</td>
                        <td><span class="status-badge {status_badge}">{status_text}</span></td>
                        <td>{links_found}</td>
                        <td>{links_broken}</td>
                        <td>{cookie_found}</td>
                        <td><button id="btn-{row_id}" class="details-btn" onclick="toggleDetails({row_id})">Show Details</button></td>
                    </tr>
                    <tr id="details-{row_id}" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> {bl_result.get('links_found', 0)}</p>
                                        <p><strong>Working:</strong> <span class="status-working">{bl_result.get('links_working', 0)}</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">{bl_result.get('links_broken', 0)}</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">{bl_result.get('links_timeout', 0)}</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">{bl_result.get('links_error', 0)}</span></p>
"""

            # Add links table if there are links
            if bl_result.get('links'):
                html_content += """
                                        <table class="links-mini-table">
                                            <tr><th>URL</th><th>Text</th><th>Status</th><th>Code</th></tr>
"""
                for link in bl_result['links'][:10]:  # Show only first 10 links to avoid clutter
                    status_class = f"status-{link['status'].replace('_', '-')}"
                    html_content += f"""
                                            <tr>
                                                <td title="{link['url']}" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">{link['url'][:50]}{'...' if len(link['url']) > 50 else ''}</td>
                                                <td>{link['text'][:30]}{'...' if len(link['text']) > 30 else ''}</td>
                                                <td class="{status_class}">{link['status']}</td>
                                                <td>{link['status_code'] or 'N/A'}</td>
                                            </tr>
"""
                html_content += "</table>"
                if len(bl_result['links']) > 10:
                    html_content += f"<p><em>... and {len(bl_result['links']) - 10} more links</em></p>"

            html_content += f"""
                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> {'Yes' if cookie_result.get('cookie_found') else 'No'}</p>
                                        <p><strong>Cookie deleted:</strong> {'Yes' if cookie_result.get('cookie_deleted') else 'No'}</p>
                                        <p><strong>Cookie value:</strong> {cookie_result.get('cookie_value', 'N/A')}</p>
                                        <p><strong>Timestamp:</strong> {url_result['timestamp']}</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
"""

        # Close the table and HTML
        html_content += """
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        self.logger.info(f"HTML report generated: {output_file}")

    def _generate_json_report(self, results: Dict[str, Any], output_file: Path):
        """Generate JSON report"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        self.logger.info(f"JSON report generated: {output_file}")

    def _generate_csv_report(self, results: Dict[str, Any], output_file: Path):
        """Generate CSV report"""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow([
                'URL', 'Overall Status', 'Timestamp', 'Links Found', 'Links Working',
                'Links Broken', 'Links Timeout', 'Links Error', 'Cookie Found',
                'Cookie Deleted', 'Cookie Value'
            ])

            # Write data
            for url, url_result in results['results'].items():
                row = [
                    url,
                    'PASS' if url_result['success'] else 'FAIL',
                    url_result['timestamp']
                ]

                # Add broken links data
                bl_result = url_result['tests'].get('broken_links', {})
                row.extend([
                    bl_result.get('links_found', 0),
                    bl_result.get('links_working', 0),
                    bl_result.get('links_broken', 0),
                    bl_result.get('links_timeout', 0),
                    bl_result.get('links_error', 0)
                ])

                # Add cookie data
                cookie_result = url_result['tests'].get('gtinfo_cookie', {})
                row.extend([
                    'Yes' if cookie_result.get('cookie_found') else 'No',
                    'Yes' if cookie_result.get('cookie_deleted') else 'No',
                    cookie_result.get('cookie_value', '')
                ])

                writer.writerow(row)

        self.logger.info(f"CSV report generated: {output_file}")

    def close(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver closed successfully")
            except Exception as e:
                self.logger.warning(f"Error closing WebDriver: {e}")

        if self.logger:
            self.logger.info("WebpageTester session ended")


class ConfigManager:
    """Configuration management system"""

    def __init__(self, config_file="config.yaml"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from YAML file"""
        try:
            if not Path(self.config_file).exists():
                self.create_default_config()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            return self.validate_config(config)
            
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
    
    def create_default_config(self):
        """Create default configuration file"""
        default_config = self.get_default_config()
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)
        
        print(f"Created default configuration file: {self.config_file}")
    
    def get_default_config(self):
        """Get default configuration"""
        return {
            "browser": {
                "type": "chrome",
                "headless": False,
                "window_size": [1920, 1080],
                "timeout": 30,
                "implicit_wait": 10
            },
            "webdriver": {
                "chrome_driver_path": None,
                "firefox_driver_path": None,
                "download_directory": "./downloads"
            },
            "testing": {
                "cookie_name": "gtinfo",
                "header_selector": "header",
                "link_selectors": ["a[href]"],
                "max_concurrent_requests": 5,
                "request_timeout": 10,
                "retry_attempts": 3
            },
            "urls": [
                "https://example.com"
            ],
            "logging": {
                "level": "INFO",
                "log_directory": "logs",
                "console_output": True,
                "file_output": True
            },
            "reporting": {
                "output_directory": "reports",
                "generate_html": True,
                "generate_json": True,
                "generate_csv": True,
                "report_title": "Webpage Testing Report"
            },
            "test_cases": {
                "broken_links": {
                    "enabled": True,
                    "description": "Check for broken links in header"
                },
                "gtinfo_cookie": {
                    "enabled": True,
                    "description": "Monitor gtinfo cookie behavior"
                }
            },
            "advanced": {
                "user_agent": None,
                "custom_headers": {},
                "proxy_settings": None,
                "page_load_strategy": "normal",
                "screenshot_on_failure": True,
                "cleanup_on_exit": True
            }
        }
    
    def validate_config(self, config):
        """Validate and sanitize configuration"""
        default = self.get_default_config()
        
        # Merge with defaults for missing keys
        def merge_dict(default_dict, user_dict):
            result = default_dict.copy()
            for key, value in user_dict.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        return merge_dict(default, config)
    
    def get(self, key_path, default=None):
        """Get configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def update_urls(self, urls):
        """Update URLs list in configuration"""
        self.config['urls'] = urls
    
    def save_config(self):
        """Save current configuration to file"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, indent=2)

def create_argument_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Webpage Testing Framework - Automated testing for web pages",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Run with default config
  python main.py --config custom.yaml              # Use custom config file
  python main.py --urls https://example.com        # Test single URL
  python main.py --headless                        # Run in headless mode
  python main.py --output-dir ./my-reports         # Custom output directory
  python main.py --log-level DEBUG                 # Enable debug logging
        """
    )
    
    # Configuration options
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='Configuration file path (default: config.yaml)'
    )
    
    # URL options
    parser.add_argument(
        '--urls', '-u',
        nargs='+',
        help='URLs to test (overrides config file)'
    )
    
    parser.add_argument(
        '--urls-file',
        help='File containing URLs to test (one per line)'
    )
    
    # Browser options
    parser.add_argument(
        '--browser',
        choices=['chrome', 'firefox', 'edge'],
        help='Browser to use (overrides config)'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode'
    )
    
    # Output options
    parser.add_argument(
        '--output-dir', '-o',
        help='Output directory for reports (overrides config)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Logging level (overrides config)'
    )
    
    # Test options
    parser.add_argument(
        '--skip-broken-links',
        action='store_true',
        help='Skip broken link detection'
    )
    
    parser.add_argument(
        '--skip-gtinfo',
        action='store_true',
        help='Skip gtinfo cookie testing'
    )
    
    # Utility options
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='Create default configuration file and exit'
    )
    
    parser.add_argument(
        '--validate-config',
        action='store_true',
        help='Validate configuration file and exit'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Webpage Testing Framework 1.0.0'
    )
    
    return parser

def load_urls_from_file(file_path):
    """Load URLs from a text file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        return urls
    except Exception as e:
        print(f"Error loading URLs from file {file_path}: {e}")
        return []

def main():
    """Main execution function"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Handle utility commands
    if args.create_config:
        config_manager = ConfigManager(args.config)
        config_manager.create_default_config()
        print(f"Default configuration created: {args.config}")
        return 0
    
    if args.validate_config:
        try:
            config_manager = ConfigManager(args.config)
            print(f"Configuration file '{args.config}' is valid")
            return 0
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return 1
    
    try:
        # Load configuration
        config_manager = ConfigManager(args.config)
        print(f"Loaded configuration from: {args.config}")
        
        # Override configuration with command line arguments
        if args.urls:
            config_manager.update_urls(args.urls)
        elif args.urls_file:
            urls_from_file = load_urls_from_file(args.urls_file)
            if urls_from_file:
                config_manager.update_urls(urls_from_file)
        
        if args.browser:
            config_manager.config['browser']['type'] = args.browser
        
        if args.headless:
            config_manager.config['browser']['headless'] = True
        
        if args.output_dir:
            config_manager.config['reporting']['output_directory'] = args.output_dir
        
        if args.log_level:
            config_manager.config['logging']['level'] = args.log_level
        
        if args.skip_broken_links:
            config_manager.config['test_cases']['broken_links']['enabled'] = False
        
        if args.skip_gtinfo:
            config_manager.config['test_cases']['gtinfo_cookie']['enabled'] = False
        
        # Get URLs to test
        urls = config_manager.get('urls', [])
        if not urls:
            print("Error: No URLs specified in configuration or command line")
            return 1
        
        print(f"Testing {len(urls)} URLs:")
        for url in urls:
            print(f"  - {url}")
        
        # Initialize WebpageTester
        tester = WebpageTester(config_manager.config)
        
        # Setup enhanced logging
        log_level = getattr(logging, config_manager.get('logging.level', 'INFO'))
        log_dir = config_manager.get('logging.log_directory', 'logs')
        session_info = tester.setup_enhanced_logging(log_dir, log_level)
        
        print(f"Logging session: {session_info['session_id']}")
        print(f"Log directory: {session_info['log_directory']}")
        
        # Run comprehensive tests
        print("\nStarting comprehensive webpage testing...")
        test_results = tester.run_comprehensive_tests(urls)
        
        if not test_results["success"]:
            print(f"Testing failed: {test_results.get('error')}")
            return 1
        
        # Generate reports
        print("\nGenerating reports...")
        report_title = config_manager.get('reporting.report_title', 'Webpage Testing Report')
        output_dir = config_manager.get('reporting.output_directory', 'reports')
        
        report_results = tester.generate_all_reports(
            test_results["results"], 
            output_dir, 
            report_title
        )
        
        if report_results["success"]:
            print(f"\nReports generated in: {report_results['output_directory']}")
            for format_type, path in report_results["reports"].items():
                print(f"  {format_type.upper()}: {path}")
        else:
            print(f"Report generation failed: {report_results.get('error')}")
        
        # Print summary
        results = test_results["results"]["results"]
        total_suites = len(results)
        successful_suites = sum(1 for r in results.values() if r.get("success"))
        
        print(f"\n{'='*60}")
        print("TESTING COMPLETED")
        print(f"{'='*60}")
        print(f"Total URLs: {total_suites}")
        print(f"Successful: {successful_suites}")
        print(f"Failed: {total_suites - successful_suites}")
        print(f"Session ID: {session_info['session_id']}")
        
        # Cleanup
        if config_manager.get('advanced.cleanup_on_exit', True):
            tester.close()
        
        return 0
        
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())