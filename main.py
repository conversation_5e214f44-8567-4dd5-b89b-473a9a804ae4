import yaml
import argparse
import sys
from pathlib import Path

class ConfigManager:
    """Configuration management system"""
    
    def __init__(self, config_file="config.yaml"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from YAML file"""
        try:
            if not Path(self.config_file).exists():
                self.create_default_config()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            return self.validate_config(config)
            
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
    
    def create_default_config(self):
        """Create default configuration file"""
        default_config = self.get_default_config()
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)
        
        print(f"Created default configuration file: {self.config_file}")
    
    def get_default_config(self):
        """Get default configuration"""
        return {
            "browser": {
                "type": "chrome",
                "headless": False,
                "window_size": [1920, 1080],
                "timeout": 30,
                "implicit_wait": 10
            },
            "webdriver": {
                "chrome_driver_path": None,
                "firefox_driver_path": None,
                "download_directory": "./downloads"
            },
            "testing": {
                "cookie_name": "gtinfo",
                "header_selector": "header",
                "link_selectors": ["a[href]"],
                "max_concurrent_requests": 5,
                "request_timeout": 10,
                "retry_attempts": 3
            },
            "urls": [
                "https://example.com"
            ],
            "logging": {
                "level": "INFO",
                "log_directory": "logs",
                "console_output": True,
                "file_output": True
            },
            "reporting": {
                "output_directory": "reports",
                "generate_html": True,
                "generate_json": True,
                "generate_csv": True,
                "report_title": "Webpage Testing Report"
            },
            "test_cases": {
                "broken_links": {
                    "enabled": True,
                    "description": "Check for broken links in header"
                },
                "gtinfo_cookie": {
                    "enabled": True,
                    "description": "Monitor gtinfo cookie behavior"
                }
            },
            "advanced": {
                "user_agent": None,
                "custom_headers": {},
                "proxy_settings": None,
                "page_load_strategy": "normal",
                "screenshot_on_failure": True,
                "cleanup_on_exit": True
            }
        }
    
    def validate_config(self, config):
        """Validate and sanitize configuration"""
        default = self.get_default_config()
        
        # Merge with defaults for missing keys
        def merge_dict(default_dict, user_dict):
            result = default_dict.copy()
            for key, value in user_dict.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        return merge_dict(default, config)
    
    def get(self, key_path, default=None):
        """Get configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def update_urls(self, urls):
        """Update URLs list in configuration"""
        self.config['urls'] = urls
    
    def save_config(self):
        """Save current configuration to file"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, indent=2)

def create_argument_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Webpage Testing Framework - Automated testing for web pages",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Run with default config
  python main.py --config custom.yaml              # Use custom config file
  python main.py --urls https://example.com        # Test single URL
  python main.py --headless                        # Run in headless mode
  python main.py --output-dir ./my-reports         # Custom output directory
  python main.py --log-level DEBUG                 # Enable debug logging
        """
    )
    
    # Configuration options
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='Configuration file path (default: config.yaml)'
    )
    
    # URL options
    parser.add_argument(
        '--urls', '-u',
        nargs='+',
        help='URLs to test (overrides config file)'
    )
    
    parser.add_argument(
        '--urls-file',
        help='File containing URLs to test (one per line)'
    )
    
    # Browser options
    parser.add_argument(
        '--browser',
        choices=['chrome', 'firefox', 'edge'],
        help='Browser to use (overrides config)'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode'
    )
    
    # Output options
    parser.add_argument(
        '--output-dir', '-o',
        help='Output directory for reports (overrides config)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Logging level (overrides config)'
    )
    
    # Test options
    parser.add_argument(
        '--skip-broken-links',
        action='store_true',
        help='Skip broken link detection'
    )
    
    parser.add_argument(
        '--skip-gtinfo',
        action='store_true',
        help='Skip gtinfo cookie testing'
    )
    
    # Utility options
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='Create default configuration file and exit'
    )
    
    parser.add_argument(
        '--validate-config',
        action='store_true',
        help='Validate configuration file and exit'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Webpage Testing Framework 1.0.0'
    )
    
    return parser

def load_urls_from_file(file_path):
    """Load URLs from a text file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        return urls
    except Exception as e:
        print(f"Error loading URLs from file {file_path}: {e}")
        return []

def main():
    """Main execution function"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Handle utility commands
    if args.create_config:
        config_manager = ConfigManager(args.config)
        config_manager.create_default_config()
        print(f"Default configuration created: {args.config}")
        return 0
    
    if args.validate_config:
        try:
            config_manager = ConfigManager(args.config)
            print(f"Configuration file '{args.config}' is valid")
            return 0
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return 1
    
    try:
        # Load configuration
        config_manager = ConfigManager(args.config)
        print(f"Loaded configuration from: {args.config}")
        
        # Override configuration with command line arguments
        if args.urls:
            config_manager.update_urls(args.urls)
        elif args.urls_file:
            urls_from_file = load_urls_from_file(args.urls_file)
            if urls_from_file:
                config_manager.update_urls(urls_from_file)
        
        if args.browser:
            config_manager.config['browser']['type'] = args.browser
        
        if args.headless:
            config_manager.config['browser']['headless'] = True
        
        if args.output_dir:
            config_manager.config['reporting']['output_directory'] = args.output_dir
        
        if args.log_level:
            config_manager.config['logging']['level'] = args.log_level
        
        if args.skip_broken_links:
            config_manager.config['test_cases']['broken_links']['enabled'] = False
        
        if args.skip_gtinfo:
            config_manager.config['test_cases']['gtinfo_cookie']['enabled'] = False
        
        # Get URLs to test
        urls = config_manager.get('urls', [])
        if not urls:
            print("Error: No URLs specified in configuration or command line")
            return 1
        
        print(f"Testing {len(urls)} URLs:")
        for url in urls:
            print(f"  - {url}")
        
        # Initialize WebpageTester
        tester = WebpageTester(config_manager.config)
        
        # Setup enhanced logging
        log_level = getattr(logging, config_manager.get('logging.level', 'INFO'))
        log_dir = config_manager.get('logging.log_directory', 'logs')
        session_info = tester.setup_enhanced_logging(log_dir, log_level)
        
        print(f"Logging session: {session_info['session_id']}")
        print(f"Log directory: {session_info['log_directory']}")
        
        # Run comprehensive tests
        print("\nStarting comprehensive webpage testing...")
        test_results = tester.run_comprehensive_tests(urls)
        
        if not test_results["success"]:
            print(f"Testing failed: {test_results.get('error')}")
            return 1
        
        # Generate reports
        print("\nGenerating reports...")
        report_title = config_manager.get('reporting.report_title', 'Webpage Testing Report')
        output_dir = config_manager.get('reporting.output_directory', 'reports')
        
        report_results = tester.generate_all_reports(
            test_results["results"], 
            output_dir, 
            report_title
        )
        
        if report_results["success"]:
            print(f"\nReports generated in: {report_results['output_directory']}")
            for format_type, path in report_results["reports"].items():
                print(f"  {format_type.upper()}: {path}")
        else:
            print(f"Report generation failed: {report_results.get('error')}")
        
        # Print summary
        results = test_results["results"]["results"]
        total_suites = len(results)
        successful_suites = sum(1 for r in results.values() if r.get("success"))
        
        print(f"\n{'='*60}")
        print("TESTING COMPLETED")
        print(f"{'='*60}")
        print(f"Total URLs: {total_suites}")
        print(f"Successful: {successful_suites}")
        print(f"Failed: {total_suites - successful_suites}")
        print(f"Session ID: {session_info['session_id']}")
        
        # Cleanup
        if config_manager.get('advanced.cleanup_on_exit', True):
            tester.close()
        
        return 0
        
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())