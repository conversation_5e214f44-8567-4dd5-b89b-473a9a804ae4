{"timestamp": "2025-07-26T20:25:33.235439", "session_id": "b962880d-6ab6-4458-b97f-d08aa5835cc2", "total_urls": 2, "results": {"https://github.com": {"url": "https://github.com", "success": true, "timestamp": "2025-07-26T20:25:35.114902", "tests": {"broken_links": {"url": "https://github.com", "success": true, "links_found": 60, "links_working": 60, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.846390962600708}, {"url": "https://github.com/login", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8731279373168945}, {"url": "https://github.com/features/models", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9554047584533691}, {"url": "https://github.com/features/spark", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9606382846832275}, {"url": "https://github.com/features/copilot", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9670200347900391}, {"url": "https://github.com/features/actions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7492101192474365}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.89040207862854}, {"url": "https://github.com/features/code-review", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8617818355560303}, {"url": "https://github.com/features/codespaces", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8959250450134277}, {"url": "https://github.com/features/issues", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.918820858001709}, {"url": "https://docs.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6946141719818115}, {"url": "https://github.com/features/discussions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9939889907836914}, {"url": "https://github.com/features", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7779090404510498}, {"url": "https://github.com/features/code-search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9384269714355469}, {"url": "https://github.com/why-github", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.938502311706543}, {"url": "https://github.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5567481517791748}, {"url": "https://skills.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7820439338684082}, {"url": "https://github.com/team", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.874647855758667}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9965391159057617}, {"url": "https://github.com/enterprise/startups", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9152350425720215}, {"url": "https://github.com/solutions/industry/nonprofits", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8930351734161377}, {"url": "https://github.com/solutions/use-case/devsecops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8674912452697754}, {"url": "https://github.com/solutions/use-case/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.936974048614502}, {"url": "https://github.com/solutions/use-case/ci-cd", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8747129440307617}, {"url": "https://github.com/solutions/use-case", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.018974781036377}, {"url": "https://github.com/solutions/industry/healthcare", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9567501544952393}, {"url": "https://github.com/solutions/industry/financial-services", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8514289855957031}, {"url": "https://github.com/solutions/industry/manufacturing", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.94468092918396}, {"url": "https://github.com/solutions/industry/government", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.975365161895752}, {"url": "https://github.com/solutions/industry", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9104278087615967}, {"url": "https://github.com/solutions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8861889839172363}, {"url": "https://github.com/resources/articles/ai", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8649048805236816}, {"url": "https://github.com/resources/articles/security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8517658710479736}, {"url": "https://github.com/resources/articles/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8997881412506104}, {"url": "https://github.com/resources/articles/software-development", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9589920043945312}, {"url": "https://github.com/resources/articles", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9150481224060059}, {"url": "https://resources.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6729249954223633}, {"url": "https://resources.github.com/learn/pathways", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.1008379459381104}, {"url": "https://github.com/resources/whitepapers", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9797499179840088}, {"url": "https://github.com/customer-stories", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8626108169555664}, {"url": "https://github.com/solutions/executive-insights", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.853147029876709}, {"url": "https://github.com/sponsors", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8847250938415527}, {"url": "https://github.com/readme", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.900493860244751}, {"url": "https://github.com/topics", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8700141906738281}, {"url": "https://github.com/collections", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8879611492156982}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8199059963226318}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7609078884124756}, {"url": "https://partner.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 2.7071099281311035}, {"url": "https://github.com/trending", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.7503840923309326}, {"url": "https://github.com/features/copilot/copilot-business", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9521300792694092}, {"url": "https://github.com/pricing", "text": "Pricing", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9181890487670898}, {"url": "https://github.com/premium-support", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.1240310668945312}, {"url": "https://github.com/enterprise?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9441893100738525}, {"url": "https://github.com/security?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0769572257995605}, {"url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5472719669342041}, {"url": "https://github.com/pricing?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8573570251464844}, {"url": "https://github.com/features/copilot?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.178361177444458}, {"url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5357682704925537}, {"url": "https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F&source=header-home", "text": "Sign up", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9003520011901855}, {"url": "https://github.com/login", "text": "Sign in", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.2401132583618164}], "error": null}, "gtinfo_cookie": {"url": "https://github.com", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}, "https://stackoverflow.com": {"url": "https://stackoverflow.com", "success": true, "timestamp": "2025-07-26T20:25:52.103643", "tests": {"broken_links": {"url": "https://stackoverflow.com", "success": true, "links_found": 22, "links_working": 17, "links_broken": 5, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://stackoverflow.co/teams/", "text": "For Teams", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8037729263305664}, {"url": "https://stackoverflow.co/", "text": "About", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8198461532592773}, {"url": "https://stackoverflow.com/questions#content", "text": "Skip to main content", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9803047180175781}, {"url": "https://stackoverflow.com/questions#", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9826521873474121}, {"url": "https://stackoverflow.co/teams/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-for-teams", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3212149143218994}, {"url": "https://stackoverflow.co/advertising/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-advertising", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.37720799446105957}, {"url": "https://stackoverflow.co/labs/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=labs", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3513648509979248}, {"url": "https://stackoverflow.co/api-solutions/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=overflow-api", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3613290786743164}, {"url": "https://stackoverflow.co/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=about-the-company", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3668050765991211}, {"url": "https://stackoverflow.com/", "text": "Stack Overflow", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.5085690021514893}, {"url": "https://stackoverflow.blog/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=blog", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5989959239959717}, {"url": "https://stackoverflow.com/help", "text": "", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.7283897399902344}, {"url": "https://stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.219939947128296}, {"url": "https://meta.stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8845701217651367}, {"url": "https://stackoverflow.com/users/signup?ssrc=site_switcher&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "", "selector": "a[href]", "status_code": 403, "status": "broken", "error": "HTTP 403", "response_time": 0.5722451210021973}, {"url": "https://chat.stackoverflow.com/?tab=explore", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.3043789863586426}, {"url": "https://stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.734740972518921}, {"url": "https://stackoverflow.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.33811521530151367}, {"url": "https://stackoverflow.com/users/login?ssrc=site_switcher&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.6884799003601074}, {"url": "https://stackoverflow.com/users/login?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "Log in", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.6854100227355957}, {"url": "https://stackoverflow.com/users/signup?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "Sign up", "selector": "a[href]", "status_code": 403, "status": "broken", "error": "HTTP 403", "response_time": 0.4764130115509033}, {"url": "https://stackexchange.com/sites", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9914360046386719}], "error": null}, "gtinfo_cookie": {"url": "https://stackoverflow.com", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}}}