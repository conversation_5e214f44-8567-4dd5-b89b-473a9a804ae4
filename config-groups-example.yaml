browser:
  type: chrome
  headless: false
  window_size: [1920, 1080]
  timeout: 30
  implicit_wait: 10

webdriver:
  chrome_driver_path: null
  firefox_driver_path: null
  download_directory: ./downloads

testing:
  cookie_name: gtinfo
  header_selector: header
  link_selectors:
  - a[href]
  max_concurrent_requests: 5
  request_timeout: 10
  retry_attempts: 3

# URL Groups - The new dynamic testing feature
url_groups:
  - name: "E-commerce Security Test"
    description: "Testing e-commerce sites for security headers and performance"
    urls:
      - "https://github.com"
      - "https://stackoverflow.com"
    test_cases: 
      - "broken_links"
      - "header_validation"
      - "ssl_certificate"
      - "page_load_time"
    # Group-specific configuration for header validation
    header_validation:
      expected_headers:
        "X-Frame-Options": "DENY"
        "X-Content-Type-Options": "nosniff"
        "Strict-Transport-Security": "max-age=31536000"
    # Group-specific configuration for page load time
    page_load_time:
      max_load_time: 3.0

  - name: "Marketing Landing Pages"
    description: "Testing marketing pages for SEO and accessibility"
    urls:
      - "https://example.com"
      - "https://httpbin.org/html"
    test_cases:
      - "broken_links"
      - "meta_tags"
      - "page_load_time"
      - "gtinfo_cookie"
    # Group-specific configuration for meta tags
    meta_tags:
      required_tags:
        - "title"
        - "description"
        - "viewport"
        - "keywords"
        - "author"
    # Different performance threshold for marketing pages
    page_load_time:
      max_load_time: 2.0

  - name: "API Endpoints"
    description: "Testing API endpoints for basic functionality"
    urls:
      - "https://httpbin.org/status/200"
      - "https://httpbin.org/json"
    test_cases:
      - "ssl_certificate"
      - "header_validation"
      - "page_load_time"
    header_validation:
      expected_headers:
        "Content-Type": "application/json"
    page_load_time:
      max_load_time: 1.0

# Backward compatibility - simple URLs list
urls:
  - https://example.com

logging:
  level: INFO
  log_directory: logs
  console_output: true
  file_output: true

reporting:
  output_directory: reports
  generate_html: true
  generate_json: true
  generate_csv: true
  report_title: Dynamic Webpage Testing Report

# Available test cases configuration
test_cases:
  broken_links:
    enabled: true
    description: Check for broken links in header
  gtinfo_cookie:
    enabled: true
    description: Monitor gtinfo cookie behavior
  header_validation:
    enabled: true
    description: Validate HTTP headers against expected values
  page_load_time:
    enabled: true
    description: Measure and validate page load performance
    max_load_time: 5.0
  ssl_certificate:
    enabled: true
    description: Validate SSL certificate status and expiration
  meta_tags:
    enabled: true
    description: Check for required meta tags
    required_tags:
    - title
    - description
    - viewport
  accessibility:
    enabled: true
    description: Check for basic accessibility requirements
  content_validation:
    enabled: true
    description: Validate presence of specific content on the page

advanced:
  user_agent: null
  custom_headers: {}
  proxy_settings: null
  page_load_strategy: normal
  screenshot_on_failure: true
  cleanup_on_exit: true
