{"timestamp": "2025-07-26T20:45:50.536626", "session_id": "9ab6a997-47ab-4031-ac8a-6aefaea6ec5f", "total_groups": 3, "total_urls": 6, "groups": {"E-commerce Security Test": {"name": "E-commerce Security Test", "urls_count": 2, "test_cases": ["broken_links", "header_validation", "ssl_certificate", "page_load_time"], "success": false, "timestamp": "2025-07-26T20:45:52.619766", "url_results": {"https://github.com": {"url": "https://github.com", "group": "E-commerce Security Test", "success": false, "timestamp": "2025-07-26T20:45:52.619792", "tests": {"broken_links": {"url": "https://github.com", "success": true, "links_found": 60, "links_working": 60, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://github.com/features/models", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0859920978546143}, {"url": "https://github.com/features/copilot", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0868191719055176}, {"url": "https://github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0907692909240723}, {"url": "https://github.com/login", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.1562612056732178}, {"url": "https://github.com/features/spark", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.2367029190063477}, {"url": "https://github.com/features/actions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9384200572967529}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9589402675628662}, {"url": "https://github.com/features/codespaces", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9661519527435303}, {"url": "https://github.com/features/issues", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.976294994354248}, {"url": "https://github.com/features/code-review", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9353699684143066}, {"url": "https://docs.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.72416090965271}, {"url": "https://github.com/features/discussions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9273416996002197}, {"url": "https://github.com/why-github", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9814181327819824}, {"url": "https://github.com/features", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9330742359161377}, {"url": "https://github.com/features/code-search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0339360237121582}, {"url": "https://skills.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5083069801330566}, {"url": "https://github.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.49424219131469727}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.900259256362915}, {"url": "https://github.com/team", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9034223556518555}, {"url": "https://github.com/enterprise/startups", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9177367687225342}, {"url": "https://github.com/solutions/industry/nonprofits", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9237611293792725}, {"url": "https://github.com/solutions/use-case/devsecops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9877800941467285}, {"url": "https://github.com/solutions/use-case/ci-cd", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9087958335876465}, {"url": "https://github.com/solutions/use-case/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9602260589599609}, {"url": "https://github.com/solutions/use-case", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9496970176696777}, {"url": "https://github.com/solutions/industry/financial-services", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9040811061859131}, {"url": "https://github.com/solutions/industry/healthcare", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0658061504364014}, {"url": "https://github.com/solutions/industry/manufacturing", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9966731071472168}, {"url": "https://github.com/solutions/industry/government", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9887597560882568}, {"url": "https://github.com/solutions/industry", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.017085075378418}, {"url": "https://github.com/solutions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9858276844024658}, {"url": "https://github.com/resources/articles/ai", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9484908580780029}, {"url": "https://github.com/resources/articles/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.008039951324463}, {"url": "https://github.com/resources/articles/security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.020982027053833}, {"url": "https://github.com/resources/articles/software-development", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9831209182739258}, {"url": "https://resources.github.com/learn/pathways", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0598819255828857}, {"url": "https://github.com/resources/articles", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.1075329780578613}, {"url": "https://resources.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7404079437255859}, {"url": "https://github.com/customer-stories", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9161779880523682}, {"url": "https://github.com/resources/whitepapers", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0951199531555176}, {"url": "https://github.com/solutions/executive-insights", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9543249607086182}, {"url": "https://github.com/sponsors", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9561200141906738}, {"url": "https://github.com/readme", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9418549537658691}, {"url": "https://github.com/topics", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9270100593566895}, {"url": "https://partner.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.7827520370483398}, {"url": "https://github.com/collections", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9169473648071289}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8233599662780762}, {"url": "https://github.com/trending", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.376128911972046}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8514480590820312}, {"url": "https://github.com/features/copilot/copilot-business", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9731340408325195}, {"url": "https://github.com/pricing", "text": "Pricing", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.940004825592041}, {"url": "https://github.com/premium-support", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.2344169616699219}, {"url": "https://github.com/enterprise?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0096960067749023}, {"url": "https://github.com/security?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0195879936218262}, {"url": "https://github.com/features/copilot?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0572447776794434}, {"url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6119868755340576}, {"url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5823140144348145}, {"url": "https://github.com/pricing?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9623119831085205}, {"url": "https://github.com/login", "text": "Sign in", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7885823249816895}, {"url": "https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F&source=header-home", "text": "Sign up", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9676120281219482}], "error": null}, "header_validation": {"url": "https://github.com", "success": false, "headers_checked": 3, "headers_passed": 1, "headers_failed": 2, "header_results": [{"header": "X-Frame-Options", "expected": "DENY", "actual": "deny", "status": "mismatch"}, {"header": "X-Content-Type-Options", "expected": "nosniff", "actual": "nosniff", "status": "match"}, {"header": "Strict-Transport-Security", "expected": "max-age=31536000", "actual": "max-age=31536000; includeSubdomains; preload", "status": "mismatch"}], "error": null}, "ssl_certificate": {"url": "https://github.com", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 194, "error": null}, "page_load_time": {"url": "https://github.com", "success": true, "load_time": 0.363, "threshold": 3.0, "status": "fast", "error": null}}}, "https://stackoverflow.com": {"url": "https://stackoverflow.com", "group": "E-commerce Security Test", "success": false, "timestamp": "2025-07-26T20:46:09.895453", "tests": {"broken_links": {"url": "https://stackoverflow.com", "success": true, "links_found": 22, "links_working": 17, "links_broken": 5, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://stackoverflow.co/", "text": "About", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.36034202575683594}, {"url": "https://stackoverflow.co/teams/", "text": "For Teams", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.36919212341308594}, {"url": "https://stackoverflow.co/teams/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-for-teams", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.345670223236084}, {"url": "https://stackoverflow.co/advertising/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-advertising", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.36948084831237793}, {"url": "https://stackoverflow.com/questions#content", "text": "Skip to main content", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7860920429229736}, {"url": "https://stackoverflow.com/questions#", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8289170265197754}, {"url": "https://stackoverflow.co/api-solutions/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=overflow-api", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.41224193572998047}, {"url": "https://stackoverflow.co/labs/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=labs", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.38923120498657227}, {"url": "https://stackoverflow.co/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=about-the-company", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3865089416503906}, {"url": "https://stackoverflow.blog/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=blog", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.40189599990844727}, {"url": "https://stackoverflow.com/", "text": "Stack Overflow", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.362914800643921}, {"url": "https://stackoverflow.com/help", "text": "", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.7004919052124023}, {"url": "https://chat.stackoverflow.com/?tab=explore", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.884972095489502}, {"url": "https://meta.stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8053960800170898}, {"url": "https://stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0583248138427734}, {"url": "https://stackoverflow.com/users/signup?ssrc=site_switcher&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "", "selector": "a[href]", "status_code": 403, "status": "broken", "error": "HTTP 403", "response_time": 0.4801599979400635}, {"url": "https://stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.3592371940612793}, {"url": "https://stackoverflow.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3696110248565674}, {"url": "https://stackoverflow.com/users/login?ssrc=site_switcher&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.6830809116363525}, {"url": "https://stackexchange.com/sites", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9027440547943115}, {"url": "https://stackoverflow.com/users/signup?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "Sign up", "selector": "a[href]", "status_code": 403, "status": "broken", "error": "HTTP 403", "response_time": 0.6072731018066406}, {"url": "https://stackoverflow.com/users/login?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "Log in", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.7965700626373291}], "error": null}, "header_validation": {"url": "https://stackoverflow.com", "success": false, "headers_checked": 3, "headers_passed": 1, "headers_failed": 2, "header_results": [{"header": "X-Frame-Options", "expected": "DENY", "actual": "SAMEORIGIN", "status": "mismatch"}, {"header": "X-Content-Type-Options", "expected": "nosniff", "actual": "nosniff", "status": "match"}, {"header": "Strict-Transport-Security", "expected": "max-age=31536000", "actual": null, "status": "missing"}], "error": null}, "ssl_certificate": {"url": "https://stackoverflow.com", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 87, "error": null}, "page_load_time": {"url": "https://stackoverflow.com", "success": true, "load_time": 1.744, "threshold": 3.0, "status": "fast", "error": null}}}}}, "Marketing Landing Pages": {"name": "Marketing Landing Pages", "urls_count": 2, "test_cases": ["broken_links", "meta_tags", "page_load_time", "gtinfo_cookie"], "success": false, "timestamp": "2025-07-26T20:46:19.953270", "url_results": {"https://example.com": {"url": "https://example.com", "group": "Marketing Landing Pages", "success": false, "timestamp": "2025-07-26T20:46:19.953287", "tests": {"broken_links": {"url": "https://example.com", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "meta_tags": {"url": "https://example.com", "success": false, "meta_tags_found": 2, "meta_tags_missing": 3, "meta_results": [{"tag": "title", "found": true, "content": ""}, {"tag": "description", "found": false, "content": null}, {"tag": "viewport", "found": true, "content": "width=device-width, initial-scale=1"}, {"tag": "keywords", "found": false, "content": null}, {"tag": "author", "found": false, "content": null}], "error": null}, "page_load_time": {"url": "https://example.com", "success": true, "load_time": 0.016, "threshold": 2.0, "status": "fast", "error": null}, "gtinfo_cookie": {"url": "https://example.com", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}, "https://httpbin.org/html": {"url": "https://httpbin.org/html", "group": "Marketing Landing Pages", "success": false, "timestamp": "2025-07-26T20:47:51.287304", "tests": {"broken_links": {"url": "https://httpbin.org/html", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "meta_tags": {"url": "https://httpbin.org/html", "success": false, "meta_tags_found": 0, "meta_tags_missing": 5, "meta_results": [{"tag": "title", "found": false, "content": null}, {"tag": "description", "found": false, "content": null}, {"tag": "viewport", "found": false, "content": null}, {"tag": "keywords", "found": false, "content": null}, {"tag": "author", "found": false, "content": null}], "error": null}, "page_load_time": {"url": "https://httpbin.org/html", "success": true, "load_time": 0.303, "threshold": 2.0, "status": "fast", "error": null}, "gtinfo_cookie": {"url": "https://httpbin.org/html", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}}}, "API Endpoints": {"name": "API Endpoints", "urls_count": 2, "test_cases": ["ssl_certificate", "header_validation", "page_load_time"], "success": false, "timestamp": "2025-07-26T20:49:43.621060", "url_results": {"https://httpbin.org/status/200": {"url": "https://httpbin.org/status/200", "group": "API Endpoints", "success": false, "timestamp": "2025-07-26T20:49:43.621088", "tests": {"ssl_certificate": {"url": "https://httpbin.org/status/200", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 387, "error": null}, "header_validation": {"url": "https://httpbin.org/status/200", "success": false, "headers_checked": 1, "headers_passed": 0, "headers_failed": 1, "header_results": [{"header": "Content-Type", "expected": "application/json", "actual": "text/html; charset=utf-8", "status": "mismatch"}], "error": null}, "page_load_time": {"url": "https://httpbin.org/status/200", "success": true, "load_time": 0.241, "threshold": 1.0, "status": "fast", "error": null}}}, "https://httpbin.org/json": {"url": "https://httpbin.org/json", "group": "API Endpoints", "success": true, "timestamp": "2025-07-26T20:49:45.487570", "tests": {"ssl_certificate": {"url": "https://httpbin.org/json", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 387, "error": null}, "header_validation": {"url": "https://httpbin.org/json", "success": true, "headers_checked": 1, "headers_passed": 1, "headers_failed": 0, "header_results": [{"header": "Content-Type", "expected": "application/json", "actual": "application/json", "status": "match"}], "error": null}, "page_load_time": {"url": "https://httpbin.org/json", "success": true, "load_time": 0.755, "threshold": 1.0, "status": "fast", "error": null}}}}}}}