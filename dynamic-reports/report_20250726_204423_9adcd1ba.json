{"timestamp": "2025-07-26T20:40:27.334950", "session_id": "9adcd1ba-7602-4842-a572-814554807498", "total_groups": 3, "total_urls": 6, "groups": {"E-commerce Security Test": {"name": "E-commerce Security Test", "urls_count": 2, "test_cases": ["broken_links", "header_validation", "ssl_certificate", "page_load_time"], "success": false, "timestamp": "2025-07-26T20:40:30.087435", "url_results": {"https://github.com": {"url": "https://github.com", "group": "E-commerce Security Test", "success": false, "timestamp": "2025-07-26T20:40:30.087460", "tests": {"broken_links": {"url": "https://github.com", "success": true, "links_found": 60, "links_working": 60, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6651928424835205}, {"url": "https://github.com/login", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7783091068267822}, {"url": "https://github.com/features/models", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8448011875152588}, {"url": "https://github.com/features/spark", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.2489452362060547}, {"url": "https://github.com/features/copilot", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.4845490455627441}, {"url": "https://github.com/features/actions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7551429271697998}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8993880748748779}, {"url": "https://github.com/features/codespaces", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8212089538574219}, {"url": "https://github.com/features/code-review", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5634949207305908}, {"url": "https://github.com/features/issues", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8310239315032959}, {"url": "https://github.com/features/discussions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7916810512542725}, {"url": "https://github.com/features/code-search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8213157653808594}, {"url": "https://github.com/why-github", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7869818210601807}, {"url": "https://docs.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6431479454040527}, {"url": "https://github.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.44319796562194824}, {"url": "https://github.com/features", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8434381484985352}, {"url": "https://skills.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6855568885803223}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7829577922821045}, {"url": "https://github.com/team", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7826440334320068}, {"url": "https://github.com/enterprise/startups", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8032369613647461}, {"url": "https://github.com/solutions/industry/nonprofits", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8333499431610107}, {"url": "https://github.com/solutions/use-case/devsecops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8347611427307129}, {"url": "https://github.com/solutions/use-case/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8007731437683105}, {"url": "https://github.com/solutions/use-case/ci-cd", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7263739109039307}, {"url": "https://github.com/solutions/use-case", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7180721759796143}, {"url": "https://github.com/solutions/industry/healthcare", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8637878894805908}, {"url": "https://github.com/solutions/industry/financial-services", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8348701000213623}, {"url": "https://github.com/solutions/industry/manufacturing", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8631072044372559}, {"url": "https://github.com/solutions/industry", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8191037178039551}, {"url": "https://github.com/solutions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7760577201843262}, {"url": "https://github.com/resources/articles/ai", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7721340656280518}, {"url": "https://github.com/solutions/industry/government", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.3633270263671875}, {"url": "https://github.com/resources/articles/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8028538227081299}, {"url": "https://github.com/resources/articles/security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8190009593963623}, {"url": "https://github.com/resources/articles/software-development", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8299598693847656}, {"url": "https://github.com/resources/articles", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8159112930297852}, {"url": "https://resources.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6882379055023193}, {"url": "https://resources.github.com/learn/pathways", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0495219230651855}, {"url": "https://github.com/customer-stories", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6210570335388184}, {"url": "https://github.com/resources/whitepapers", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8655800819396973}, {"url": "https://github.com/solutions/executive-insights", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8281948566436768}, {"url": "https://github.com/readme", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6814992427825928}, {"url": "https://github.com/sponsors", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8808331489562988}, {"url": "https://github.com/topics", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8321590423583984}, {"url": "https://partner.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.6995000839233398}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6153590679168701}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.603151798248291}, {"url": "https://github.com/collections", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8317849636077881}, {"url": "https://github.com/trending", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.555311918258667}, {"url": "https://github.com/features/copilot/copilot-business", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8274359703063965}, {"url": "https://github.com/pricing", "text": "Pricing", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8439962863922119}, {"url": "https://github.com/enterprise?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.807884931564331}, {"url": "https://github.com/premium-support", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0784037113189697}, {"url": "https://github.com/security?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8978168964385986}, {"url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5667247772216797}, {"url": "https://github.com/features/copilot?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9535040855407715}, {"url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.5843241214752197}, {"url": "https://github.com/pricing?ref_loc=search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8447511196136475}, {"url": "https://github.com/login", "text": "Sign in", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6356589794158936}, {"url": "https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F&source=header-home", "text": "Sign up", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8718161582946777}], "error": null}, "header_validation": {"url": "https://github.com", "success": false, "headers_checked": 3, "headers_passed": 1, "headers_failed": 2, "header_results": [{"header": "X-Frame-Options", "expected": "DENY", "actual": "deny", "status": "mismatch"}, {"header": "X-Content-Type-Options", "expected": "nosniff", "actual": "nosniff", "status": "match"}, {"header": "Strict-Transport-Security", "expected": "max-age=31536000", "actual": "max-age=31536000; includeSubdomains; preload", "status": "mismatch"}], "error": null}, "ssl_certificate": {"url": "https://github.com", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 194, "error": null}, "page_load_time": {"url": "https://github.com", "success": true, "load_time": 0.607, "threshold": 3.0, "status": "fast", "error": null}}}, "https://stackoverflow.com": {"url": "https://stackoverflow.com", "group": "E-commerce Security Test", "success": false, "timestamp": "2025-07-26T20:40:46.683811", "tests": {"broken_links": {"url": "https://stackoverflow.com", "success": true, "links_found": 22, "links_working": 17, "links_broken": 5, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://stackoverflow.co/teams/", "text": "For Teams", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.32755112648010254}, {"url": "https://stackoverflow.co/", "text": "About", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3279728889465332}, {"url": "https://stackoverflow.co/teams/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-for-teams", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3404529094696045}, {"url": "https://stackoverflow.co/advertising/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-advertising", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3412511348724365}, {"url": "https://stackoverflow.com/questions#content", "text": "Skip to main content", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7527740001678467}, {"url": "https://stackoverflow.com/questions#", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7559990882873535}, {"url": "https://stackoverflow.com/", "text": "Stack Overflow", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9714581966400146}, {"url": "https://stackoverflow.co/api-solutions/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=overflow-api", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3220548629760742}, {"url": "https://stackoverflow.co/labs/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=labs", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.33037686347961426}, {"url": "https://stackoverflow.blog/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=blog", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.312222957611084}, {"url": "https://stackoverflow.co/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=about-the-company", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3160409927368164}, {"url": "https://stackoverflow.com/help", "text": "", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.7988150119781494}, {"url": "https://chat.stackoverflow.com/?tab=explore", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7957360744476318}, {"url": "https://meta.stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8489348888397217}, {"url": "https://stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.1333229541778564}, {"url": "https://stackoverflow.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.146064043045044}, {"url": "https://stackoverflow.com/users/signup?ssrc=site_switcher&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "", "selector": "a[href]", "status_code": 403, "status": "broken", "error": "HTTP 403", "response_time": 0.535398006439209}, {"url": "https://stackoverflow.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.3395881652832031}, {"url": "https://stackexchange.com/sites", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6721889972686768}, {"url": "https://stackoverflow.com/users/login?ssrc=site_switcher&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.7466750144958496}, {"url": "https://stackoverflow.com/users/signup?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "Sign up", "selector": "a[href]", "status_code": 403, "status": "broken", "error": "HTTP 403", "response_time": 0.4742758274078369}, {"url": "https://stackoverflow.com/users/login?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2fquestions", "text": "Log in", "selector": "a[href]", "status_code": 404, "status": "broken", "error": "HTTP 404", "response_time": 0.7715921401977539}], "error": null}, "header_validation": {"url": "https://stackoverflow.com", "success": false, "headers_checked": 3, "headers_passed": 0, "headers_failed": 3, "header_results": [{"header": "X-Frame-Options", "expected": "DENY", "actual": "SAMEORIGIN", "status": "mismatch"}, {"header": "X-Content-Type-Options", "expected": "nosniff", "actual": null, "status": "missing"}, {"header": "Strict-Transport-Security", "expected": "max-age=31536000", "actual": "max-age=31536000; includeSubDomains", "status": "mismatch"}], "error": null}, "ssl_certificate": {"url": "https://stackoverflow.com", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 87, "error": null}, "page_load_time": {"url": "https://stackoverflow.com", "success": true, "load_time": 1.659, "threshold": 3.0, "status": "fast", "error": null}}}}}, "Marketing Landing Pages": {"name": "Marketing Landing Pages", "urls_count": 2, "test_cases": ["broken_links", "meta_tags", "page_load_time", "gtinfo_cookie"], "success": false, "timestamp": "2025-07-26T20:40:56.409221", "url_results": {"https://example.com": {"url": "https://example.com", "group": "Marketing Landing Pages", "success": false, "timestamp": "2025-07-26T20:40:56.409239", "tests": {"broken_links": {"url": "https://example.com", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "meta_tags": {"url": "https://example.com", "success": false, "meta_tags_found": 2, "meta_tags_missing": 3, "meta_results": [{"tag": "title", "found": true, "content": ""}, {"tag": "description", "found": false, "content": null}, {"tag": "viewport", "found": true, "content": "width=device-width, initial-scale=1"}, {"tag": "keywords", "found": false, "content": null}, {"tag": "author", "found": false, "content": null}], "error": null}, "page_load_time": {"url": "https://example.com", "success": true, "load_time": 0.015, "threshold": 2.0, "status": "fast", "error": null}, "gtinfo_cookie": {"url": "https://example.com", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}, "https://httpbin.org/html": {"url": "https://httpbin.org/html", "group": "Marketing Landing Pages", "success": false, "timestamp": "2025-07-26T20:42:28.146612", "tests": {"broken_links": {"url": "https://httpbin.org/html", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "meta_tags": {"url": "https://httpbin.org/html", "success": false, "meta_tags_found": 0, "meta_tags_missing": 5, "meta_results": [{"tag": "title", "found": false, "content": null}, {"tag": "description", "found": false, "content": null}, {"tag": "viewport", "found": false, "content": null}, {"tag": "keywords", "found": false, "content": null}, {"tag": "author", "found": false, "content": null}], "error": null}, "page_load_time": {"url": "https://httpbin.org/html", "success": true, "load_time": 0.227, "threshold": 2.0, "status": "fast", "error": null}, "gtinfo_cookie": {"url": "https://httpbin.org/html", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}}}, "API Endpoints": {"name": "API Endpoints", "urls_count": 2, "test_cases": ["ssl_certificate", "header_validation", "page_load_time"], "success": false, "timestamp": "2025-07-26T20:44:20.589232", "url_results": {"https://httpbin.org/status/200": {"url": "https://httpbin.org/status/200", "group": "API Endpoints", "success": false, "timestamp": "2025-07-26T20:44:20.589253", "tests": {"ssl_certificate": {"url": "https://httpbin.org/status/200", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 387, "error": null}, "header_validation": {"url": "https://httpbin.org/status/200", "success": false, "headers_checked": 1, "headers_passed": 0, "headers_failed": 1, "header_results": [{"header": "Content-Type", "expected": "application/json", "actual": "text/html; charset=utf-8", "status": "mismatch"}], "error": null}, "page_load_time": {"url": "https://httpbin.org/status/200", "success": true, "load_time": 0.222, "threshold": 1.0, "status": "fast", "error": null}}}, "https://httpbin.org/json": {"url": "https://httpbin.org/json", "group": "API Endpoints", "success": true, "timestamp": "2025-07-26T20:44:22.316435", "tests": {"ssl_certificate": {"url": "https://httpbin.org/json", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 387, "error": null}, "header_validation": {"url": "https://httpbin.org/json", "success": true, "headers_checked": 1, "headers_passed": 1, "headers_failed": 0, "header_results": [{"header": "Content-Type", "expected": "application/json", "actual": "application/json", "status": "match"}], "error": null}, "page_load_time": {"url": "https://httpbin.org/json", "success": true, "load_time": 0.229, "threshold": 1.0, "status": "fast", "error": null}}}}}}}