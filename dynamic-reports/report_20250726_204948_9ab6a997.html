
<!DOCTYPE html>
<html>
<head>
    <title>Dynamic Webpage Testing Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; }
        .header h1 { margin: 0 0 10px 0; font-size: 2.5em; }
        .header p { margin: 5px 0; opacity: 0.9; }

        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2.5em; font-weight: bold; margin: 10px 0; }
        .summary-card .label { color: #666; font-size: 0.9em; }

        .success-number { color: #4CAF50; }
        .failure-number { color: #f44336; }
        .warning-number { color: #ff9800; }
        .info-number { color: #2196F3; }

        .results-section { margin-top: 30px; }
        .results-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .filter-buttons { display: flex; gap: 10px; }
        .filter-btn { padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; transition: all 0.3s; }
        .filter-btn:hover { background: #f0f0f0; }
        .filter-btn.active { background: #2196F3; color: white; border-color: #2196F3; }

        .results-table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .results-table th { background: #f8f9fa; padding: 15px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; }
        .results-table td { padding: 12px 15px; border-bottom: 1px solid #e9ecef; }
        .results-table tr:hover { background: #f8f9fa; }

        .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.85em; font-weight: 500; }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }

        .url-cell { max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .url-cell:hover { overflow: visible; white-space: normal; word-break: break-all; }

        .details-btn { background: #17a2b8; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 0.8em; }
        .details-btn:hover { background: #138496; }

        .details-row { display: none; }
        .details-content { background: #f8f9fa; padding: 20px; }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .detail-section { background: white; padding: 15px; border-radius: 4px; border: 1px solid #e0e0e0; }
        .detail-section h4 { margin: 0 0 10px 0; color: #333; }

        .links-mini-table { width: 100%; font-size: 0.85em; }
        .links-mini-table th, .links-mini-table td { padding: 4px 8px; }
        .status-working { color: #4CAF50; font-weight: 500; }
        .status-broken { color: #f44336; font-weight: 500; }
        .status-timeout { color: #ff9800; font-weight: 500; }
        .status-error { color: #9c27b0; font-weight: 500; }

        @media (max-width: 768px) {
            .summary-grid { grid-template-columns: 1fr; }
            .details-grid { grid-template-columns: 1fr; }
            .filter-buttons { flex-wrap: wrap; }
        }
    </style>
    <script>
        function toggleDetails(rowId) {
            const detailsRow = document.getElementById('details-' + rowId);
            const btn = document.getElementById('btn-' + rowId);
            if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {
                detailsRow.style.display = 'table-row';
                btn.textContent = 'Hide Details';
            } else {
                detailsRow.style.display = 'none';
                btn.textContent = 'Show Details';
            }
        }

        function filterResults(status) {
            const rows = document.querySelectorAll('.result-row');
            const buttons = document.querySelectorAll('.filter-btn');

            buttons.forEach(btn => btn.classList.remove('active'));
            document.getElementById('filter-' + status).classList.add('active');

            rows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = 'table-row';
                } else {
                    row.style.display = 'none';
                    // Hide details row too
                    const detailsRow = document.getElementById('details-' + row.dataset.id);
                    if (detailsRow) detailsRow.style.display = 'none';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('filter-all').classList.add('active');
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Dynamic Webpage Testing Report</h1>
            <p>Generated: 2025-07-26T20:45:50.536626</p>
            <p>Session ID: 9ab6a997-47ab-4031-ac8a-6aefaea6ec5f</p>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h3>URLs Tested</h3>
                <div class="number info-number">6</div>
                <div class="label">Total websites</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="number success-number">1</div>
                <div class="label">Successful tests</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="number failure-number">5</div>
                <div class="label">Failed tests</div>
            </div>
            <div class="summary-card">
                <h3>Links Found</h3>
                <div class="number info-number">82</div>
                <div class="label">Total links checked</div>
            </div>
            <div class="summary-card">
                <h3>Working Links</h3>
                <div class="number success-number">77</div>
                <div class="label">Healthy links</div>
            </div>
            <div class="summary-card">
                <h3>Broken Links</h3>
                <div class="number failure-number">5</div>
                <div class="label">Failed links</div>
            </div>
            <div class="summary-card">
                <h3>Cookies Found</h3>
                <div class="number warning-number">0</div>
                <div class="label">GTInfo cookies</div>
            </div>
            <div class="summary-card">
                <h3>Cookies Deleted</h3>
                <div class="number success-number">0</div>
                <div class="label">Successfully removed</div>
            </div>
        </div>

        <div class="results-section">
            <div class="results-header">
                <h2>Detailed Results</h2>
                <div class="filter-buttons">
                    <button id="filter-all" class="filter-btn" onclick="filterResults('all')">All (6)</button>
                    <button id="filter-pass" class="filter-btn" onclick="filterResults('pass')">Passed (1)</button>
                    <button id="filter-fail" class="filter-btn" onclick="filterResults('fail')">Failed (5)</button>
                </div>
            </div>

            <table class="results-table">
                <thead>
                    <tr>
                        <th>URL</th>
                        <th>Status</th>
                        <th>Links</th>
                        <th>Broken</th>
                        <th>Cookie</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>

                    <tr class="result-row" data-status="fail" data-id="1">
                        <td class="url-cell" title="https://github.com">https://github.com</td>
                        <td><span class="status-badge status-fail">FAIL</span></td>
                        <td>60</td>
                        <td>0</td>
                        <td>No</td>
                        <td><button id="btn-1" class="details-btn" onclick="toggleDetails(1)">Show Details</button></td>
                    </tr>
                    <tr id="details-1" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> 60</p>
                                        <p><strong>Working:</strong> <span class="status-working">60</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">0</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">0</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">0</span></p>

                                        <table class="links-mini-table">
                                            <tr><th>URL</th><th>Text</th><th>Status</th><th>Code</th></tr>

                                            <tr>
                                                <td title="https://github.com/features/models" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/models</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/features/copilot" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/copilot</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/login" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/login</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/features/spark" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/spark</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/features/actions" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/actions</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/security/advanced-security" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/security/advanced-security</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/features/codespaces" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/codespaces</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/features/issues" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/issues</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://github.com/features/code-review" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://github.com/features/code-review</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>
</table><p><em>... and 50 more links</em></p>
                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> No</p>
                                        <p><strong>Cookie deleted:</strong> No</p>
                                        <p><strong>Cookie value:</strong> N/A</p>
                                        <p><strong>Timestamp:</strong> 2025-07-26T20:45:52.619792</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr class="result-row" data-status="fail" data-id="2">
                        <td class="url-cell" title="https://stackoverflow.com">https://stackoverflow.com</td>
                        <td><span class="status-badge status-fail">FAIL</span></td>
                        <td>22</td>
                        <td>5</td>
                        <td>No</td>
                        <td><button id="btn-2" class="details-btn" onclick="toggleDetails(2)">Show Details</button></td>
                    </tr>
                    <tr id="details-2" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> 22</p>
                                        <p><strong>Working:</strong> <span class="status-working">17</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">5</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">0</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">0</span></p>

                                        <table class="links-mini-table">
                                            <tr><th>URL</th><th>Text</th><th>Status</th><th>Code</th></tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/</td>
                                                <td>About</td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/teams/" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/teams/</td>
                                                <td>For Teams</td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/teams/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-for-teams" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/teams/?utm_medium=referra...</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/advertising/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=stack-overflow-advertising" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/advertising/?utm_medium=r...</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.com/questions#content" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.com/questions#content</td>
                                                <td>Skip to main content</td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.com/questions#" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.com/questions#</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/api-solutions/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=overflow-api" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/api-solutions/?utm_medium...</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/labs/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=labs" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/labs/?utm_medium=referral...</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.co/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=about-the-company" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.co/?utm_medium=referral&utm_...</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>

                                            <tr>
                                                <td title="https://stackoverflow.blog/?utm_medium=referral&utm_source=stackoverflow-community&utm_campaign=top-nav&utm_content=blog" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">https://stackoverflow.blog/?utm_medium=referral&ut...</td>
                                                <td></td>
                                                <td class="status-working">working</td>
                                                <td>200</td>
                                            </tr>
</table><p><em>... and 12 more links</em></p>
                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> No</p>
                                        <p><strong>Cookie deleted:</strong> No</p>
                                        <p><strong>Cookie value:</strong> N/A</p>
                                        <p><strong>Timestamp:</strong> 2025-07-26T20:46:09.895453</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr class="result-row" data-status="fail" data-id="3">
                        <td class="url-cell" title="https://example.com">https://example.com</td>
                        <td><span class="status-badge status-fail">FAIL</span></td>
                        <td>0</td>
                        <td>0</td>
                        <td>No</td>
                        <td><button id="btn-3" class="details-btn" onclick="toggleDetails(3)">Show Details</button></td>
                    </tr>
                    <tr id="details-3" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> 0</p>
                                        <p><strong>Working:</strong> <span class="status-working">0</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">0</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">0</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">0</span></p>

                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> No</p>
                                        <p><strong>Cookie deleted:</strong> No</p>
                                        <p><strong>Cookie value:</strong> None</p>
                                        <p><strong>Timestamp:</strong> 2025-07-26T20:46:19.953287</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr class="result-row" data-status="fail" data-id="4">
                        <td class="url-cell" title="https://httpbin.org/html">https://httpbin.org/html</td>
                        <td><span class="status-badge status-fail">FAIL</span></td>
                        <td>0</td>
                        <td>0</td>
                        <td>No</td>
                        <td><button id="btn-4" class="details-btn" onclick="toggleDetails(4)">Show Details</button></td>
                    </tr>
                    <tr id="details-4" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> 0</p>
                                        <p><strong>Working:</strong> <span class="status-working">0</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">0</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">0</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">0</span></p>

                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> No</p>
                                        <p><strong>Cookie deleted:</strong> No</p>
                                        <p><strong>Cookie value:</strong> None</p>
                                        <p><strong>Timestamp:</strong> 2025-07-26T20:47:51.287304</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr class="result-row" data-status="fail" data-id="5">
                        <td class="url-cell" title="https://httpbin.org/status/200">https://httpbin.org/status/200</td>
                        <td><span class="status-badge status-fail">FAIL</span></td>
                        <td>0</td>
                        <td>0</td>
                        <td>No</td>
                        <td><button id="btn-5" class="details-btn" onclick="toggleDetails(5)">Show Details</button></td>
                    </tr>
                    <tr id="details-5" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> 0</p>
                                        <p><strong>Working:</strong> <span class="status-working">0</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">0</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">0</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">0</span></p>

                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> No</p>
                                        <p><strong>Cookie deleted:</strong> No</p>
                                        <p><strong>Cookie value:</strong> N/A</p>
                                        <p><strong>Timestamp:</strong> 2025-07-26T20:49:43.621088</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr class="result-row" data-status="pass" data-id="6">
                        <td class="url-cell" title="https://httpbin.org/json">https://httpbin.org/json</td>
                        <td><span class="status-badge status-pass">PASS</span></td>
                        <td>0</td>
                        <td>0</td>
                        <td>No</td>
                        <td><button id="btn-6" class="details-btn" onclick="toggleDetails(6)">Show Details</button></td>
                    </tr>
                    <tr id="details-6" class="details-row">
                        <td colspan="6">
                            <div class="details-content">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h4>Broken Links Test</h4>
                                        <p><strong>Links found:</strong> 0</p>
                                        <p><strong>Working:</strong> <span class="status-working">0</span></p>
                                        <p><strong>Broken:</strong> <span class="status-broken">0</span></p>
                                        <p><strong>Timeout:</strong> <span class="status-timeout">0</span></p>
                                        <p><strong>Error:</strong> <span class="status-error">0</span></p>

                                    </div>
                                    <div class="detail-section">
                                        <h4>GTInfo Cookie Test</h4>
                                        <p><strong>Cookie found:</strong> No</p>
                                        <p><strong>Cookie deleted:</strong> No</p>
                                        <p><strong>Cookie value:</strong> N/A</p>
                                        <p><strong>Timestamp:</strong> 2025-07-26T20:49:45.487570</p>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
