advanced:
  cleanup_on_exit: true
  custom_headers: {}
  page_load_strategy: normal
  proxy_settings: null
  screenshot_on_failure: true
  user_agent: null
browser:
  headless: false
  implicit_wait: 10
  timeout: 30
  type: chrome
  window_size:
  - 1920
  - 1080
logging:
  console_output: true
  file_output: true
  level: INFO
  log_directory: logs
reporting:
  generate_csv: true
  generate_html: true
  generate_json: true
  output_directory: reports
  report_title: Webpage Testing Report
test_cases:
  broken_links:
    description: Check for broken links in header
    enabled: true
  gtinfo_cookie:
    description: Monitor gtinfo cookie behavior
    enabled: true
testing:
  cookie_name: gtinfo
  header_selector: header
  link_selectors:
  - a[href]
  max_concurrent_requests: 5
  request_timeout: 10
  retry_attempts: 3
urls:
- https://example.com
webdriver:
  chrome_driver_path: null
  download_directory: ./downloads
  firefox_driver_path: null
