{"timestamp": "2025-07-26T20:50:07.769953", "session_id": "429aad27-aa2a-40c4-8bc0-e3e877f29241", "total_groups": 3, "total_urls": 6, "groups": {"E-commerce Security Test": {"name": "E-commerce Security Test", "urls_count": 2, "test_cases": ["broken_links", "header_validation", "ssl_certificate", "page_load_time"], "success": false, "timestamp": "2025-07-26T20:50:09.739485", "url_results": {"https://github.com": {"url": "https://github.com", "group": "E-commerce Security Test", "success": false, "timestamp": "2025-07-26T20:50:09.739510", "tests": {"broken_links": {"url": "https://github.com", "success": true, "links_found": 52, "links_working": 52, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [{"url": "https://github.com/login", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6600430011749268}, {"url": "https://github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6758377552032471}, {"url": "https://github.com/features/models", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.868959903717041}, {"url": "https://github.com/features/spark", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.873460054397583}, {"url": "https://github.com/features/copilot", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9023609161376953}, {"url": "https://github.com/features/actions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8153941631317139}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8455328941345215}, {"url": "https://github.com/features/code-review", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6183662414550781}, {"url": "https://github.com/features/codespaces", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.832679033279419}, {"url": "https://github.com/features/issues", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8712618350982666}, {"url": "https://github.com/features/discussions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8028349876403809}, {"url": "https://github.com/features/code-search", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7886316776275635}, {"url": "https://github.com/why-github", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9192202091217041}, {"url": "https://github.com/features", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8107941150665283}, {"url": "https://docs.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8474369049072266}, {"url": "https://skills.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.4671440124511719}, {"url": "https://github.blog/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.47713804244995117}, {"url": "https://github.com/team", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7884061336517334}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8836531639099121}, {"url": "https://github.com/enterprise/startups", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8473560810089111}, {"url": "https://github.com/solutions/use-case/devsecops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8199331760406494}, {"url": "https://github.com/solutions/industry/nonprofits", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8508930206298828}, {"url": "https://github.com/solutions/use-case/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8439579010009766}, {"url": "https://github.com/solutions/use-case/ci-cd", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.856799840927124}, {"url": "https://github.com/solutions/use-case", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9326608180999756}, {"url": "https://github.com/solutions/industry/financial-services", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8404040336608887}, {"url": "https://github.com/solutions/industry/healthcare", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8841361999511719}, {"url": "https://github.com/solutions/industry/government", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8499798774719238}, {"url": "https://github.com/solutions/industry/manufacturing", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9748289585113525}, {"url": "https://github.com/solutions", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7473909854888916}, {"url": "https://github.com/solutions/industry", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.9298708438873291}, {"url": "https://github.com/resources/articles/ai", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8293459415435791}, {"url": "https://github.com/resources/articles/devops", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8310410976409912}, {"url": "https://github.com/resources/articles/security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8636219501495361}, {"url": "https://github.com/resources/articles/software-development", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.822526216506958}, {"url": "https://github.com/resources/articles", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8616843223571777}, {"url": "https://resources.github.com/learn/pathways", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0953929424285889}, {"url": "https://resources.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7278997898101807}, {"url": "https://github.com/customer-stories", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6422007083892822}, {"url": "https://github.com/resources/whitepapers", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8161249160766602}, {"url": "https://github.com/solutions/executive-insights", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8422272205352783}, {"url": "https://github.com/sponsors", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.802818775177002}, {"url": "https://github.com/readme", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8654637336730957}, {"url": "https://github.com/topics", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.818469762802124}, {"url": "https://partner.github.com/", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.7320530414581299}, {"url": "https://github.com/enterprise", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6277601718902588}, {"url": "https://github.com/collections", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.7874009609222412}, {"url": "https://github.com/security/advanced-security", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.6254217624664307}, {"url": "https://github.com/trending", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.4518249034881592}, {"url": "https://github.com/features/copilot/copilot-business", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8075897693634033}, {"url": "https://github.com/pricing", "text": "Pricing", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 0.8293850421905518}, {"url": "https://github.com/premium-support", "text": "", "selector": "a[href]", "status_code": 200, "status": "working", "error": null, "response_time": 1.0588319301605225}], "error": null}, "header_validation": {"url": "https://github.com", "success": false, "headers_checked": 3, "headers_passed": 1, "headers_failed": 2, "header_results": [{"header": "X-Frame-Options", "expected": "DENY", "actual": "deny", "status": "mismatch"}, {"header": "X-Content-Type-Options", "expected": "nosniff", "actual": "nosniff", "status": "match"}, {"header": "Strict-Transport-Security", "expected": "max-age=31536000", "actual": "max-age=31536000; includeSubdomains; preload", "status": "mismatch"}], "error": null}, "ssl_certificate": {"url": "https://github.com", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 194, "error": null}, "page_load_time": {"url": "https://github.com", "success": true, "load_time": 0.334, "threshold": 3.0, "status": "fast", "error": null}}}, "https://stackoverflow.com": {"url": "https://stackoverflow.com", "group": "E-commerce Security Test", "success": false, "timestamp": "2025-07-26T20:50:23.433120", "tests": {"broken_links": {"url": "https://stackoverflow.com", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "header_validation": {"url": "https://stackoverflow.com", "success": false, "headers_checked": 3, "headers_passed": 1, "headers_failed": 2, "header_results": [{"header": "X-Frame-Options", "expected": "DENY", "actual": "SAMEORIGIN", "status": "mismatch"}, {"header": "X-Content-Type-Options", "expected": "nosniff", "actual": "nosniff", "status": "match"}, {"header": "Strict-Transport-Security", "expected": "max-age=31536000", "actual": null, "status": "missing"}], "error": null}, "ssl_certificate": {"url": "https://stackoverflow.com", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 87, "error": null}, "page_load_time": {"url": "https://stackoverflow.com", "success": true, "load_time": 0.922, "threshold": 3.0, "status": "fast", "error": null}}}}}, "Marketing Landing Pages": {"name": "Marketing Landing Pages", "urls_count": 2, "test_cases": ["broken_links", "meta_tags", "page_load_time", "gtinfo_cookie"], "success": false, "timestamp": "2025-07-26T20:51:27.832813", "url_results": {"https://example.com": {"url": "https://example.com", "group": "Marketing Landing Pages", "success": false, "timestamp": "2025-07-26T20:51:27.832832", "tests": {"broken_links": {"url": "https://example.com", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "meta_tags": {"url": "https://example.com", "success": false, "meta_tags_found": 2, "meta_tags_missing": 3, "meta_results": [{"tag": "title", "found": true, "content": ""}, {"tag": "description", "found": false, "content": null}, {"tag": "viewport", "found": true, "content": "width=device-width, initial-scale=1"}, {"tag": "keywords", "found": false, "content": null}, {"tag": "author", "found": false, "content": null}], "error": null}, "page_load_time": {"url": "https://example.com", "success": true, "load_time": 0.014, "threshold": 2.0, "status": "fast", "error": null}, "gtinfo_cookie": {"url": "https://example.com", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}, "https://httpbin.org/html": {"url": "https://httpbin.org/html", "group": "Marketing Landing Pages", "success": false, "timestamp": "2025-07-26T20:52:58.904248", "tests": {"broken_links": {"url": "https://httpbin.org/html", "success": true, "links_found": 0, "links_working": 0, "links_broken": 0, "links_timeout": 0, "links_error": 0, "links": [], "error": null}, "meta_tags": {"url": "https://httpbin.org/html", "success": false, "meta_tags_found": 0, "meta_tags_missing": 5, "meta_results": [{"tag": "title", "found": false, "content": null}, {"tag": "description", "found": false, "content": null}, {"tag": "viewport", "found": false, "content": null}, {"tag": "keywords", "found": false, "content": null}, {"tag": "author", "found": false, "content": null}], "error": null}, "page_load_time": {"url": "https://httpbin.org/html", "success": true, "load_time": 0.214, "threshold": 2.0, "status": "fast", "error": null}, "gtinfo_cookie": {"url": "https://httpbin.org/html", "success": true, "cookie_found": false, "cookie_deleted": false, "cookie_value": null, "error": null}}}}}, "API Endpoints": {"name": "API Endpoints", "urls_count": 2, "test_cases": ["ssl_certificate", "header_validation", "page_load_time"], "success": false, "timestamp": "2025-07-26T20:54:50.952037", "url_results": {"https://httpbin.org/status/200": {"url": "https://httpbin.org/status/200", "group": "API Endpoints", "success": false, "timestamp": "2025-07-26T20:54:50.952057", "tests": {"ssl_certificate": {"url": "https://httpbin.org/status/200", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 387, "error": null}, "header_validation": {"url": "https://httpbin.org/status/200", "success": false, "headers_checked": 1, "headers_passed": 0, "headers_failed": 1, "header_results": [{"header": "Content-Type", "expected": "application/json", "actual": "text/html; charset=utf-8", "status": "mismatch"}], "error": null}, "page_load_time": {"url": "https://httpbin.org/status/200", "success": true, "load_time": 0.213, "threshold": 1.0, "status": "fast", "error": null}}}, "https://httpbin.org/json": {"url": "https://httpbin.org/json", "group": "API Endpoints", "success": true, "timestamp": "2025-07-26T20:54:52.655094", "tests": {"ssl_certificate": {"url": "https://httpbin.org/json", "success": true, "is_https": true, "certificate_valid": true, "expires_soon": false, "days_until_expiry": 387, "error": null}, "header_validation": {"url": "https://httpbin.org/json", "success": true, "headers_checked": 1, "headers_passed": 1, "headers_failed": 0, "header_results": [{"header": "Content-Type", "expected": "application/json", "actual": "application/json", "status": "match"}], "error": null}, "page_load_time": {"url": "https://httpbin.org/json", "success": true, "load_time": 0.274, "threshold": 1.0, "status": "fast", "error": null}}}}}}}