2025-07-26 20:50:07,769 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Logging session started: 429aad27-aa2a-40c4-8bc0-e3e877f29241
2025-07-26 20:50:07,769 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Starting group-based tests for 3 groups, 6 total URLs
2025-07-26 20:50:09,739 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - WebDriver initialized: chrome
2025-07-26 20:50:09,739 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing group 'E-commerce Security Test' with 2 URLs and 4 test cases
2025-07-26 20:50:09,739 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing URL: https://github.com (Group: E-commerce Security Test)
2025-07-26 20:50:09,739 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking broken links for: https://github.com
2025-07-26 20:50:12,583 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Loaded page: https://github.com
2025-07-26 20:50:12,822 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - WARNING - Error finding links with selector 'a[href]': Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#staleelementreferenceexception
Stacktrace:
0   chromedriver                        0x0000000104a4b570 cxxbridge1$str$ptr + 2731064
1   chromedriver                        0x0000000104a43468 cxxbridge1$str$ptr + 2698032
2   chromedriver                        0x00000001045923f8 cxxbridge1$string$len + 90664
3   chromedriver                        0x00000001045980b0 cxxbridge1$string$len + 114400
4   chromedriver                        0x000000010459a67c cxxbridge1$string$len + 124076
5   chromedriver                        0x000000010461b81c cxxbridge1$string$len + 652876
6   chromedriver                        0x000000010461ab1c cxxbridge1$string$len + 649548
7   chromedriver                        0x00000001045cda0c cxxbridge1$string$len + 333884
8   chromedriver                        0x0000000104a0e5f4 cxxbridge1$str$ptr + 2481340
9   chromedriver                        0x0000000104a1185c cxxbridge1$str$ptr + 2494244
10  chromedriver                        0x00000001049ef248 cxxbridge1$str$ptr + 2353424
11  chromedriver                        0x0000000104a12118 cxxbridge1$str$ptr + 2496480
12  chromedriver                        0x00000001049e02f8 cxxbridge1$str$ptr + 2292160
13  chromedriver                        0x0000000104a3200c cxxbridge1$str$ptr + 2627284
14  chromedriver                        0x0000000104a32198 cxxbridge1$str$ptr + 2627680
15  chromedriver                        0x0000000104a430a4 cxxbridge1$str$ptr + 2697068
16  libsystem_pthread.dylib             0x000000019c2d1c0c _pthread_start + 136
17  libsystem_pthread.dylib             0x000000019c2ccb80 thread_start + 8

2025-07-26 20:50:12,823 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Found 52 links in header of https://github.com
2025-07-26 20:50:22,047 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Broken link check completed for https://github.com: 52 working, 0 broken
2025-07-26 20:50:22,048 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'broken_links' for https://github.com: PASS
2025-07-26 20:50:22,048 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Validating HTTP headers for: https://github.com
2025-07-26 20:50:22,666 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Header validation completed for https://github.com: 1 passed, 2 failed
2025-07-26 20:50:22,666 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'header_validation' for https://github.com: FAIL
2025-07-26 20:50:22,666 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking SSL certificate for: https://github.com
2025-07-26 20:50:23,097 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - SSL certificate for https://github.com: Valid, expires in 194 days
2025-07-26 20:50:23,098 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'ssl_certificate' for https://github.com: PASS
2025-07-26 20:50:23,098 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Measuring page load time for: https://github.com
2025-07-26 20:50:23,432 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Page load time for https://github.com: 0.334s (threshold: 3.0s)
2025-07-26 20:50:23,432 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'page_load_time' for https://github.com: PASS
2025-07-26 20:50:23,432 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed testing for https://github.com: FAIL
2025-07-26 20:50:23,433 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing URL: https://stackoverflow.com (Group: E-commerce Security Test)
2025-07-26 20:50:23,433 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking broken links for: https://stackoverflow.com
2025-07-26 20:50:25,306 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Loaded page: https://stackoverflow.com
2025-07-26 20:51:25,524 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - WARNING - No header element found on https://stackoverflow.com
2025-07-26 20:51:25,524 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'broken_links' for https://stackoverflow.com: PASS
2025-07-26 20:51:25,524 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Validating HTTP headers for: https://stackoverflow.com
2025-07-26 20:51:26,376 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Header validation completed for https://stackoverflow.com: 1 passed, 2 failed
2025-07-26 20:51:26,376 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'header_validation' for https://stackoverflow.com: FAIL
2025-07-26 20:51:26,376 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking SSL certificate for: https://stackoverflow.com
2025-07-26 20:51:26,909 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - SSL certificate for https://stackoverflow.com: Valid, expires in 87 days
2025-07-26 20:51:26,910 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'ssl_certificate' for https://stackoverflow.com: PASS
2025-07-26 20:51:26,910 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Measuring page load time for: https://stackoverflow.com
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Page load time for https://stackoverflow.com: 0.922s (threshold: 3.0s)
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'page_load_time' for https://stackoverflow.com: PASS
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed testing for https://stackoverflow.com: FAIL
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed group 'E-commerce Security Test': FAIL
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing group 'Marketing Landing Pages' with 2 URLs and 4 test cases
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing URL: https://example.com (Group: Marketing Landing Pages)
2025-07-26 20:51:27,832 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking broken links for: https://example.com
2025-07-26 20:51:28,644 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Loaded page: https://example.com
2025-07-26 20:52:28,770 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - WARNING - No header element found on https://example.com
2025-07-26 20:52:28,770 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'broken_links' for https://example.com: PASS
2025-07-26 20:52:28,771 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking meta tags for: https://example.com
2025-07-26 20:52:58,875 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Meta tags check for https://example.com: 2 found, 3 missing
2025-07-26 20:52:58,875 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'meta_tags' for https://example.com: FAIL
2025-07-26 20:52:58,875 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Measuring page load time for: https://example.com
2025-07-26 20:52:58,890 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Page load time for https://example.com: 0.014s (threshold: 2.0s)
2025-07-26 20:52:58,890 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'page_load_time' for https://example.com: PASS
2025-07-26 20:52:58,890 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Managing gtinfo cookie for: https://example.com
2025-07-26 20:52:58,904 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - No gtinfo cookie found
2025-07-26 20:52:58,904 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'gtinfo_cookie' for https://example.com: PASS
2025-07-26 20:52:58,904 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed testing for https://example.com: FAIL
2025-07-26 20:52:58,904 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing URL: https://httpbin.org/html (Group: Marketing Landing Pages)
2025-07-26 20:52:58,904 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking broken links for: https://httpbin.org/html
2025-07-26 20:52:59,550 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Loaded page: https://httpbin.org/html
2025-07-26 20:53:59,690 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - WARNING - No header element found on https://httpbin.org/html
2025-07-26 20:53:59,690 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'broken_links' for https://httpbin.org/html: PASS
2025-07-26 20:53:59,690 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking meta tags for: https://httpbin.org/html
2025-07-26 20:54:50,522 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Meta tags check for https://httpbin.org/html: 0 found, 5 missing
2025-07-26 20:54:50,523 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'meta_tags' for https://httpbin.org/html: FAIL
2025-07-26 20:54:50,523 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Measuring page load time for: https://httpbin.org/html
2025-07-26 20:54:50,737 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Page load time for https://httpbin.org/html: 0.214s (threshold: 2.0s)
2025-07-26 20:54:50,737 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'page_load_time' for https://httpbin.org/html: PASS
2025-07-26 20:54:50,737 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Managing gtinfo cookie for: https://httpbin.org/html
2025-07-26 20:54:50,951 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - No gtinfo cookie found
2025-07-26 20:54:50,951 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'gtinfo_cookie' for https://httpbin.org/html: PASS
2025-07-26 20:54:50,951 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed testing for https://httpbin.org/html: FAIL
2025-07-26 20:54:50,951 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed group 'Marketing Landing Pages': FAIL
2025-07-26 20:54:50,952 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing group 'API Endpoints' with 2 URLs and 3 test cases
2025-07-26 20:54:50,952 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing URL: https://httpbin.org/status/200 (Group: API Endpoints)
2025-07-26 20:54:50,952 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking SSL certificate for: https://httpbin.org/status/200
2025-07-26 20:54:51,620 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - SSL certificate for https://httpbin.org/status/200: Valid, expires in 387 days
2025-07-26 20:54:51,620 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'ssl_certificate' for https://httpbin.org/status/200: PASS
2025-07-26 20:54:51,620 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Validating HTTP headers for: https://httpbin.org/status/200
2025-07-26 20:54:52,441 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Header validation completed for https://httpbin.org/status/200: 0 passed, 1 failed
2025-07-26 20:54:52,442 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'header_validation' for https://httpbin.org/status/200: FAIL
2025-07-26 20:54:52,442 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Measuring page load time for: https://httpbin.org/status/200
2025-07-26 20:54:52,654 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Page load time for https://httpbin.org/status/200: 0.213s (threshold: 1.0s)
2025-07-26 20:54:52,655 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'page_load_time' for https://httpbin.org/status/200: PASS
2025-07-26 20:54:52,655 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed testing for https://httpbin.org/status/200: FAIL
2025-07-26 20:54:52,655 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Testing URL: https://httpbin.org/json (Group: API Endpoints)
2025-07-26 20:54:52,655 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Checking SSL certificate for: https://httpbin.org/json
2025-07-26 20:54:53,240 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - SSL certificate for https://httpbin.org/json: Valid, expires in 387 days
2025-07-26 20:54:53,243 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'ssl_certificate' for https://httpbin.org/json: PASS
2025-07-26 20:54:53,243 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Validating HTTP headers for: https://httpbin.org/json
2025-07-26 20:54:54,188 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Header validation completed for https://httpbin.org/json: 1 passed, 0 failed
2025-07-26 20:54:54,188 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'header_validation' for https://httpbin.org/json: PASS
2025-07-26 20:54:54,189 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Measuring page load time for: https://httpbin.org/json
2025-07-26 20:54:54,463 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Page load time for https://httpbin.org/json: 0.274s (threshold: 1.0s)
2025-07-26 20:54:54,463 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Test 'page_load_time' for https://httpbin.org/json: PASS
2025-07-26 20:54:54,463 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed testing for https://httpbin.org/json: PASS
2025-07-26 20:54:54,463 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Completed group 'API Endpoints': FAIL
2025-07-26 20:54:54,463 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Group-based tests completed successfully
2025-07-26 20:54:54,463 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - Generating reports in: ./final-test-reports
2025-07-26 20:54:54,464 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - HTML report generated: final-test-reports/report_20250726_205454_429aad27.html
2025-07-26 20:54:54,465 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - JSON report generated: final-test-reports/report_20250726_205454_429aad27.json
2025-07-26 20:54:54,465 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - CSV report generated: final-test-reports/report_20250726_205454_429aad27.csv
2025-07-26 20:54:54,465 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - All reports generated successfully
2025-07-26 20:54:54,590 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - WebDriver closed successfully
2025-07-26 20:54:54,590 - WebpageTester_429aad27-aa2a-40c4-8bc0-e3e877f29241 - INFO - WebpageTester session ended
